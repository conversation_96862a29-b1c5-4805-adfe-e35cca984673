package appium;

import java.time.Duration;
import org.openqa.selenium.By;
import org.openqa.selenium.NoSuchElementException;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.testng.annotations.*;

import io.appium.java_client.android.AndroidDriver;
import pro.truongsinh.appium_flutter.FlutterFinder;
import utils.TestUtils;

public class LaunchTechTom {
    private FlutterFinder find;
    private AndroidDriver driver;
    private WebDriverWait wait;
    private String launcherApp;

    @BeforeClass
    @Parameters({ "language", "locale", "testName" })
    public void setup(String language, String locale, String testName) throws Exception {
        launcherApp = System.getProperty("launcherApp");
        if (launcherApp.equals("Delphi")) {
            driver = DriverManager.getDelphiDriver(language, locale);
        } else if (launcherApp.equals("TurkeyLauncher")) {
            driver = DriverManager.getTechTomLauncherDriver(language, locale);
        } else if (launcherApp.equals("MOT")) {
            driver = DriverManager.getMotDriver(language, locale);
        } else {
            driver = DriverManager.getTechTomDriver(language, locale, testName);
        }

        find = new FlutterFinder(driver);
        wait = new WebDriverWait(driver,
                Duration.ofSeconds(60));
    }

    @Test
    public void launchTechTom() throws InterruptedException {
        if (launcherApp.equals("Delphi")) {
            launchFromDelphi();
        } else if (launcherApp.equals("TurkeyLauncher")) {
            launchFromTechTomLauncher();
        } else if (launcherApp.equals("MOT")) {
            launchFromMot();
        }
    }

    private void launchFromDelphi() throws InterruptedException {
        // Wait for app to load
        Thread.sleep(6000);

        // Give access to all files
        TestUtils.clickToAndroidElement(By.xpath("//*[@text='JA']"), wait);
        Thread.sleep(2000);

        // Turn on the switch for Delphi app to give access
        TestUtils.clickToAndroidElement(By.xpath(
                "//*[@text='DM-GIT']"),
                wait);

        // Go back to the app
        TestUtils.clickToAndroidElement(By.xpath("//*[@class='android.widget.ImageButton']"),
                wait);
        Thread.sleep(2000);

        // Find and fill username field
        wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("//*[@resource-id='ion-input-0']")));
        driver.findElement(By.xpath("//*[@resource-id='ion-input-0']"))
                .sendKeys("gaIoannis");

        // Find and fill password field
        wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("//*[@resource-id='ion-input-1']")));
        driver.findElement(By.xpath("//*[@resource-id='ion-input-1']"))
                .sendKeys("STart01_!!");

        // Click the Anmelden (Login) button
        TestUtils.clickToAndroidElement(By.xpath("//*[@text='ANMELDEN']"), wait);

        // Wait for login to complete and verify success
        Thread.sleep(20000);

        // If optional update, reject it
        try {
            if (driver.findElement(By.xpath("//*[@text='Optionales Update']")).isDisplayed()) {
                TestUtils.clickToAndroidElement(By.xpath("//*[@text='ABBRECHEN']"), wait);
            }
        } catch (NoSuchElementException e) {
            System.out.println("No optional update");
        }

        Thread.sleep(20000);

        // Check if landed on home screen
        TestUtils.clickToAndroidElement(
                By.xpath("//*[@text='SK, KAI, ZA24, 004-0090164/25, aw']"),
                wait);
        Thread.sleep(2000);

        TestUtils.clickToAndroidElement(By.xpath("//*[@resource-id='TTS400Am-zwischenebene-popover-buttons']"),
                wait);
        Thread.sleep(2000);

        // Launch Tech TOM
        TestUtils.clickToAndroidElement(
                By.xpath("//*[@text='' and @class='android.view.View' and ./*[@text='Tech TOM']]"), wait);
    }

    private void launchFromTechTomLauncher() throws InterruptedException {
        // Tap on Dropdown Menu
        //TestUtils.clickToElement(find.byValueKey("_DropdownMenu_"), driver);
        //TestUtils.clickToElement(find.text("com.vodafone.techtom.tr.uat"), driver);

        // Tap on launching app
        //TestUtils.clickToElement(find.text("Automation Turkey"), driver);
        TestUtils.scrollDownUntilElementIsVisible(find.text("Automation Turkey"), driver);
        TestUtils.clickToElement(find.text("Automation Turkey"), driver);
        Thread.sleep(2000);
    }

    private void launchFromMot() throws InterruptedException {
        // Alow Vodafone to make and manage phone calls
        TestUtils.clickToAndroidElement(By.xpath("//*[@text='Allow']"), wait);

        // Tap on settings and choose environment
        TestUtils.clickToAndroidElement(By.xpath("//*[@text='Settings']"), wait);
        Thread.sleep(3000);
        TestUtils.clickToAndroidElement(By.xpath("//*[@text='Select a server address']"), wait);
        TestUtils.clickToAndroidElement(By.xpath("//*[@text='TEST3']"), wait);
        driver.navigate().back();

        // Find and fill username field
        wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("//*[@resource-id='username']")));
        driver.findElement(By.xpath("//*[@resource-id='username']"))
                .sendKeys("krey.ruediger_fs");

        // Find and fill password field
        wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("//*[@resource-id='password']")));
        driver.findElement(By.xpath("//*[@resource-id='password']"))
                .sendKeys("Baumkrone123!");

        // Tap on login button
        TestUtils.clickToAndroidElement(By.xpath("//*[@resource-id='loginButton']"), wait);

        Thread.sleep(3000);

        // If update, accept it
        try {
            if (driver.findElement(By.xpath("//*[@text='Application update']")).isDisplayed()) {
                TestUtils.clickToAndroidElement(By.xpath("//*[@text='Start download']"), wait);
            }
            Thread.sleep(20000);

            // Go to settings and give permission to update
            TestUtils.clickToAndroidElement(By.xpath("//*[@text='Settings']"), wait);
            TestUtils.clickToAndroidElement(By.xpath("//*[@text='Vodafone']"), wait);
            TestUtils.clickToAndroidElement(By.xpath("//*[@text='Update']"), wait);
            TestUtils.clickToAndroidElement(By.xpath("//*[@class='android.widget.ImageButton']"),
                    wait);

            // Scroll and open the MOT app again
            TestUtils.scrollUpAndroid(driver);
            TestUtils.clickToAndroidElement(By.xpath("//*[@text='Vodafone MOT']"), wait);

            // Find and fill username field
            wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("//*[@resource-id='username']")));
            driver.findElement(By.xpath("//*[@resource-id='username']"))
                    .sendKeys("krey.ruediger_fs");

            // Find and fill password field
            wait.until(ExpectedConditions.presenceOfElementLocated(By.xpath("//*[@resource-id='password']")));
            driver.findElement(By.xpath("//*[@resource-id='password']"))
                    .sendKeys("Baumkrone123!");

            // Tap on login button
            TestUtils.clickToAndroidElement(By.xpath("//*[@resource-id='loginButton']"), wait);

            Thread.sleep(200000);
        } catch (NoSuchElementException e) {
            System.out.println("No update");
        }

        // Scroll and tap on TechTOM item from the menu
        TestUtils.scrollUpAndroid(driver);
        TestUtils.clickToAndroidElement(By.xpath("//*[@text='TechTOM']"), wait);
        Thread.sleep(2000);
    }

    @AfterClass
    public void tearDown() {
        DriverManager.quitDrivers();
    }
}
