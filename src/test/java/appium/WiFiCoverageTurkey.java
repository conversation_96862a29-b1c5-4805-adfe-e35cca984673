package appium;



import org.openqa.selenium.By;

import org.openqa.selenium.JavascriptExecutor;

import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;

import org.testng.annotations.*;
import org.testng.annotations.Parameters;

import java.time.Duration;
import java.util.List;
import java.util.Set;


import io.appium.java_client.android.AndroidDriver;

import pro.truongsinh.appium_flutter.FlutterFinder;

import utils.TestUtils;

public class WiFiCoverageTurkey {
    private FlutterFinder find;
    private AndroidDriver driver;

    @BeforeClass
    @Parameters({"language", "locale", "testName"})
    public void setup(String language, String locale, String testName) throws Exception {
        driver = DriverManager.getTechTomDriver(language, locale, testName);
        find = new FlutterFinder(driver);
    }

    @Test
    public  void wifiCoverageTrTest() throws InterruptedException{

        //Go to Test & Reports Screen
        TestUtils.clickToElement(find.byValueKey("_Tests&Reports_Tab_"), driver);

        //Start WiFi Coverage Flow
        TestUtils.clickToElement(find.byValueKey("_WiFiCoverageCheck_Button_"), driver);

        //Start Coverage Test
        TestUtils.clickToElement(find.text("Start Coverage Test"), driver);

        //Start scan for the router
        TestUtils.clickToElement(find.text("Start scan"), driver);

        //
        // Wait for app/webview to load
        Thread.sleep(5000);

        //Click on close button
        TestUtils.clickToElement(find.byValueKey("_CloseButton_"), driver);
        TestUtils.clickToElement(find.byValueKey("_Cancel_"), driver);

        // List available contexts
        Set<String> contexts = driver.getContextHandles();
        // goes to Gradle problems-report.html
        System.out.println("Available contexts: " + contexts);

        driver.context("WEBVIEW_com.vodafone.techtom.tr.uat");
        //driver.findElement(By.cssSelector("#main-content > div.button__wrapper > div > button")).click();
        JavascriptExecutor js = (JavascriptExecutor) driver;
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(15));
        WebElement goButton = wait.until(ExpectedConditions.elementToBeClickable(By.cssSelector("button.button.background-primary-hover.text-primary")));

        //goButton.click();
        js.executeScript("arguments[0].click();", goButton);

        // --- Wait until results stabilize ---
        WebDriverWait waitResults = new WebDriverWait(driver, Duration.ofSeconds(120));

        String[] finalSpeeds = waitResults.until(d -> {
            try {
                List<WebElement> elems = d.findElements(By.cssSelector("div.number.monochrome-primary"));
                if (elems.size() >= 2) {
                    String d1 = elems.get(0).getText().trim();
                    String u1 = elems.get(1).getText().trim();

                    // ignore empty values
                    if (!d1.isEmpty() && !u1.isEmpty()) {
                        try { Thread.sleep(3000); } catch (InterruptedException e) {}

                        // Refetch elements to avoid stale element reference
                        List<WebElement> freshElems = d.findElements(By.cssSelector("div.number.monochrome-primary"));
                        if (freshElems.size() >= 2) {
                            String d2 = freshElems.get(0).getText().trim();
                            String u2 = freshElems.get(1).getText().trim();

                            if (d1.equals(d2) && u1.equals(u2)) {
                                return new String[]{d2, u2}; // stable
                            }
                        }
                    }
                }
            } catch (Exception e) {
                // Handle stale element or other exceptions by continuing to wait
                System.out.println("Retrying due to: " + e.getMessage());
            }
            return null; // keep waiting
        });

        // Wait a bit more to ensure the webview is completely done
        Thread.sleep(2000);

        // Switch back to Flutter with verification
        switchToFlutterContext();

        //Click on close button
        System.out.println("Clicking close button...");
        TestUtils.clickToElement(find.byValueKey("_CloseButton_"), driver);

        // Wait for UI to stabilize after closing
        Thread.sleep(3000);
        System.out.println("Closed speed test dialog, waiting for UI to stabilize...");

        // Tap on expand button for the router and check if speedtest is already added
        TestUtils.clickToElement(find.byValueKey("_ExpandOrCollapseRouter_Button_"), driver);
        driver.executeScript("flutter:waitFor", find.byValueKey("_DownloadSpeed_Router_"), 1000);
        driver.executeScript("flutter:waitFor", find.byValueKey("_UploadSpeed_Router_"), 1000);

        // Collapse the item
        TestUtils.clickToElement(find.byValueKey("_ExpandOrCollapseRouter_Button_"), driver);


        // Check if there's another close button that needs to be clicked
        /*try {
            driver.executeScript("flutter:waitFor", find.byValueKey("_CloseButton_"), 3000);
            System.out.println("Found another close button, clicking it...");
            TestUtils.clickToElement(find.byValueKey("_CloseButton_"), driver);
            Thread.sleep(2000);
            System.out.println("Clicked additional close button");
        } catch (Exception e) {
            System.out.println("No additional close button found");
        }*/

        // Check if we're back to the main coverage screen by looking for expected elements
        /*try {
            // Wait for the main screen to be ready - try the pattern from working WiFiCoverageCheck
            driver.executeScript("flutter:waitFor", find.text("Initial check"), 15000);
            System.out.println("Back on Initial check screen");
        } catch (Exception e) {
            System.out.println("Initial check text not found, trying WiFi Coverage...");
            try {
                driver.executeScript("flutter:waitFor", find.text("WiFi Coverage"), 10000);
                System.out.println("Back on WiFi Coverage screen");
            } catch (Exception e2) {
                System.out.println("WiFi Coverage text not found, trying Coverage...");
                try {
                    driver.executeScript("flutter:waitFor", find.text("Coverage"), 5000);
                    System.out.println("Found Coverage text");
                } catch (Exception e3) {
                    System.out.println("No expected screen text found, proceeding anyway...");
                }
            }
        }*/

        // Try to find the router expand button with more patience
        /*System.out.println("Looking for router expand button...");
        try {
            driver.executeScript("flutter:waitFor", find.byValueKey("_ExpandOrCollapseRouter_Button_"), 15000);
            System.out.println("Found router expand button, clicking it...");
            TestUtils.clickToElement(find.byValueKey("_ExpandOrCollapseRouter_Button_"), driver);

            // Check if speedtest results are already there
            System.out.println("Checking for speed test results...");
            driver.executeScript("flutter:waitFor", find.byValueKey("_DownloadSpeed_Router_"), 5000);
            driver.executeScript("flutter:waitFor", find.byValueKey("_UploadSpeed_Router_"), 5000);
            System.out.println("Speed test results found!");

            // Collapse the item
            System.out.println("Collapsing router item...");
            TestUtils.clickToElement(find.byValueKey("_ExpandOrCollapseRouter_Button_"), driver);

        } catch (Exception e) {
            System.err.println("Could not find or interact with router expand button: " + e.getMessage());
            // Let's see what elements are actually available
            debugAvailableElements();

            // Try alternative router button patterns
            System.out.println("Trying alternative router button patterns...");
            String[] alternativeRouterKeys = {
                "_Router_Button_",
                "_RouterButton_",
                "_ExpandRouter_",
                "_CollapseRouter_",
                "_Router_ExpandCollapse_"
            };

            boolean foundAlternative = false;
            for (String altKey : alternativeRouterKeys) {
                try {
                    driver.executeScript("flutter:waitFor", find.byValueKey(altKey), 3000);
                    System.out.println("Found alternative router button: " + altKey);
                    TestUtils.clickToElement(find.byValueKey(altKey), driver);
                    foundAlternative = true;
                    break;
                } catch (Exception altE) {
                    // Continue trying other alternatives
                }
            }*/

            /*if (!foundAlternative) {
                System.out.println("No router button found. Checking if we need additional navigation steps...");

                // Check if there are additional close buttons or dialogs to dismiss
                handleAdditionalDialogs();

                // Try one more time to find the router button after handling dialogs
                try {
                    Thread.sleep(2000);
                    driver.executeScript("flutter:waitFor", find.byValueKey("_ExpandOrCollapseRouter_Button_"), 10000);
                    System.out.println("Found router button after handling additional dialogs!");
                    TestUtils.clickToElement(find.byValueKey("_ExpandOrCollapseRouter_Button_"), driver);

                    // Check for speed test results
                    driver.executeScript("flutter:waitFor", find.byValueKey("_DownloadSpeed_Router_"), 5000);
                    driver.executeScript("flutter:waitFor", find.byValueKey("_UploadSpeed_Router_"), 5000);
                    System.out.println("Speed test results found after additional navigation!");

                    // Collapse the item
                    TestUtils.clickToElement(find.byValueKey("_ExpandOrCollapseRouter_Button_"), driver);

                } catch (Exception finalE) {
                    System.out.println("Final attempt failed. Trying simple navigation approach...");
                    simpleNavigationToMainScreen();
                }
            }*/
        }




    }

    /*public static String[] waitForSpeedTestResults(AppiumDriver driver) {
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(120));

        return wait.until(d -> {
            try {
                List<WebElement> elems = d.findElements(By.cssSelector("div.number.monochrome-primary"));

                if (elems.size() >= 2) {
                    String d1 = elems.get(0).getText().trim();
                    String u1 = elems.get(1).getText().trim();

                    if (!d1.isEmpty() && !u1.isEmpty()) {
                        try { Thread.sleep(4000); } catch (InterruptedException ignored) {}

                        // Refetch elements to avoid stale element reference
                        List<WebElement> freshElems = d.findElements(By.cssSelector("div.number.monochrome-primary"));
                        if (freshElems.size() >= 2) {
                            String d2 = freshElems.get(0).getText().trim();
                            String u2 = freshElems.get(1).getText().trim();

                            // values must be stable
                            if (d1.equals(d2) && u1.equals(u2)) {
                                // wait extra time for UI rendering (final numbers)
                                try { Thread.sleep(5000); } catch (InterruptedException ignored) {}
                                return new String[]{d2, u2};
                            }
                        }
                    }
                }
            } catch (Exception e) {
                // Handle stale element or other exceptions by continuing to wait
                System.out.println("Retrying speed test wait due to: " + e.getMessage());
            }
            return null; // keep waiting
        });*/


    private void switchToFlutterContext() throws InterruptedException {
        try {
            // List available contexts to debug
            Set<String> contexts = driver.getContextHandles();
            System.out.println("Available contexts before switching: " + contexts);

            // Switch to Flutter context
            driver.context("FLUTTER");

            // Verify the switch was successful
            String currentContext = driver.getContext();
            System.out.println("Current context after switch: " + currentContext);

            // Wait a moment for context to stabilize
            Thread.sleep(1000);

        } catch (Exception e) {
            System.err.println("Error switching to Flutter context: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * Alternative method using a more robust approach for waiting for speed test completion
     * This method can be used instead of the inline waiting logic if needed
     */
   /* private String[] waitForSpeedTestCompletionRobust() {
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(120));

        return wait.until(d -> {
            try {
                // Check if speed test is still running by looking for loading indicators
                List<WebElement> loadingElements = d.findElements(By.cssSelector(".loading, .spinner, .progress"));
                if (!loadingElements.isEmpty()) {
                    return null; // Still loading, keep waiting
                }

                // Look for the speed results
                List<WebElement> speedElements = d.findElements(By.cssSelector("div.number.monochrome-primary"));
                if (speedElements.size() >= 2) {
                    String download = speedElements.get(0).getText().trim();
                    String upload = speedElements.get(1).getText().trim();

                    // Check if we have valid numeric results
                    if (!download.isEmpty() && !upload.isEmpty() &&
                        !download.equals("0") && !upload.equals("0") &&
                        !download.equals("--") && !upload.equals("--")) {

                        // Wait a bit and check again for stability
                        try { Thread.sleep(3000); } catch (InterruptedException ignored) {}

                        // Refetch to ensure values are still the same
                        List<WebElement> freshElements = d.findElements(By.cssSelector("div.number.monochrome-primary"));
                        if (freshElements.size() >= 2) {
                            String downloadCheck = freshElements.get(0).getText().trim();
                            String uploadCheck = freshElements.get(1).getText().trim();

                            if (download.equals(downloadCheck) && upload.equals(uploadCheck)) {
                                return new String[]{download, upload};
                            }
                        }
                    }
                }
            } catch (Exception e) {
                System.out.println("Retrying speed test completion check: " + e.getMessage());
            }
            return null; // Keep waiting
        });*/


    /**
     * Debug method to help identify what elements are available when the expected ones are not found
     */
    /*private void debugAvailableElements() {
        try {
            System.out.println("=== DEBUG: Available Flutter Elements ===");

            // Try to find common text elements that might indicate current screen
            String[] commonTexts = {"WiFi Coverage", "Coverage", "Router", "Start", "Close", "Back", "Continue", "Test"};
            for (String text : commonTexts) {
                try {
                    driver.executeScript("flutter:waitFor", find.text(text), 1000);
                    System.out.println("Found text: " + text);
                } catch (Exception e) {
                    // Element not found, continue
                }
            }

            // Try to find common button elements - expanded list for better debugging
            String[] commonKeys = {
                "_CloseButton_", "_CloseButton1_", "_CloseButton2_",
                "_BackButton_", "_ContinueButton_", "_StartButton_",
                "_Router_", "_ExpandOrCollapseRouter_Button_",
                "_Cancel_", "_OK_", "_Done_", "_Finish_",
                "_WiFiCoverageCheck_Button_", "_Tests&Reports_Tab_",
                "_DownloadSpeed_Router_", "_UploadSpeed_Router_"
            };
            for (String key : commonKeys) {
                try {
                    driver.executeScript("flutter:waitFor", find.byValueKey(key), 1000);
                    System.out.println("Found element with key: " + key);
                } catch (Exception e) {
                    // Element not found, continue
                }
            }

            System.out.println("=== END DEBUG ===");

        } catch (Exception e) {
            System.err.println("Error during debugging: " + e.getMessage());
        }*/


    /**
     * Handle additional dialogs or overlays that might be present after closing the speed test
     */
    /*private void handleAdditionalDialogs() {
        try {
            System.out.println("Checking for additional dialogs to dismiss...");

            // Check for additional close buttons
            String[] additionalCloseButtons = {
                "_CloseButton_", "_CloseButton2_", "_CloseButton3_",
                "_Cancel_", "_OK_", "_Done_", "_Dismiss_"
            };

            for (String closeButton : additionalCloseButtons) {
                try {
                    driver.executeScript("flutter:waitFor", find.byValueKey(closeButton), 2000);
                    System.out.println("Found additional dialog with button: " + closeButton + ", clicking it...");
                    TestUtils.clickToElement(find.byValueKey(closeButton), driver);
                    Thread.sleep(1000); // Wait for dialog to close
                    break; // Only click one close button at a time
                } catch (Exception e) {
                    // Button not found, continue
                }
            }

            // Check for back navigation if we're in a nested screen
            try {
                driver.executeScript("flutter:waitFor", find.byValueKey("_BackButton_"), 2000);
                System.out.println("Found back button, navigating back...");
                TestUtils.clickToElement(find.byValueKey("_BackButton_"), driver);
                Thread.sleep(2000);
            } catch (Exception e) {
                // No back button found
            }

            // Check if we need to navigate back to the main coverage screen
            try {
                // Look for navigation tabs or main screen indicators
                driver.executeScript("flutter:waitFor", find.byValueKey("_Tests&Reports_Tab_"), 3000);
                System.out.println("Found Tests&Reports tab, we might be on main screen");
            } catch (Exception e) {
                System.out.println("Not on main screen, might need additional navigation");
            }

        } catch (Exception e) {
            System.err.println("Error handling additional dialogs: " + e.getMessage());
        }*/


    /**
     * Alternative simpler approach - just try to get back to the main screen step by step
     * Call this method if the main approach doesn't work
     */
    /*private void simpleNavigationToMainScreen() throws InterruptedException {
        System.out.println("=== SIMPLE NAVIGATION APPROACH ===");

        // Step 1: Click any available close button
        String[] closeButtons = {"_CloseButton_", "_CloseButton1_", "_CloseButton2_"};
        for (String closeBtn : closeButtons) {
            try {
                driver.executeScript("flutter:waitFor", find.byValueKey(closeBtn), 2000);
                System.out.println("Clicking " + closeBtn);
                TestUtils.clickToElement(find.byValueKey(closeBtn), driver);
                Thread.sleep(2000);
            } catch (Exception e) {
                // Button not found, continue
            }
        }

        // Step 2: Try to navigate back if needed
        try {
            driver.executeScript("flutter:waitFor", find.byValueKey("_BackButton_"), 2000);
            System.out.println("Navigating back...");
            TestUtils.clickToElement(find.byValueKey("_BackButton_"), driver);
            Thread.sleep(2000);
        } catch (Exception e) {
            System.out.println("No back button found");
        }

        // Step 3: Check if we can find the router button now
        try {
            driver.executeScript("flutter:waitFor", find.byValueKey("_ExpandOrCollapseRouter_Button_"), 5000);
            System.out.println("SUCCESS: Found router button after simple navigation!");
            return;
        } catch (Exception e) {
            System.out.println("Router button still not found after simple navigation");
        }

        // Step 4: Try to restart the WiFi coverage flow
        try {
            driver.executeScript("flutter:waitFor", find.byValueKey("_WiFiCoverageCheck_Button_"), 3000);
            System.out.println("Found WiFi Coverage button, we're back on main screen");
        } catch (Exception e) {
            System.out.println("Not on main screen. Manual intervention may be needed.");
        }

        System.out.println("=== END SIMPLE NAVIGATION ===");
    }*/

}