package appium;

import java.net.URI;
import java.net.URL;
import java.time.Duration;
import java.util.List;
import java.util.Objects;

import org.openqa.selenium.remote.DesiredCapabilities;
import org.openqa.selenium.support.ui.WebDriverWait;

import io.appium.java_client.android.AndroidDriver;

public class DriverManager {
    private static AndroidDriver techTomDriver;
    private static AndroidDriver delphiDriver;
    private static AndroidDriver motDriver;
    private static AndroidDriver techTomLauncherDriver;

    public static AndroidDriver getTechTomDriver(String language, String locale, String testName) throws Exception {
        if (techTomDriver == null) {
            techTomDriver = createTechTomDriver(language, locale, testName);
        }
        return techTomDriver;
    }

    public static AndroidDriver getTechTomLauncherDriver(String language, String locale) throws Exception {
        if (techTomLauncherDriver == null) {
            techTomLauncherDriver = createTechTomLauncherDriver(language, locale);
        }
        return techTomLauncherDriver;
    }

    public static AndroidDriver getDelphiDriver(String language, String locale) throws Exception {
        if (delphiDriver == null) {
            delphiDriver = createDelphiDriver(language, locale);
        }
        return delphiDriver;
    }

    public static AndroidDriver getMotDriver(String language, String locale) throws Exception {
        if (motDriver == null) {
            motDriver = createMotDriver(language, locale);
        }
        return motDriver;
    }

    public static void quitDrivers() {
        if (techTomDriver != null) {
            techTomDriver.quit();
            techTomDriver = null;
        }

        if (delphiDriver != null) {
            Object testId = delphiDriver.getCapabilities().getCapability("digitalai:reportTestId");
            delphiDriver.quit();
            try {
                Thread.sleep(60000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            ObservatoryUrlManager.getAttachmentsByTestID(testId);
            delphiDriver = null;
        }

        if (motDriver != null) {
            Object testId = motDriver.getCapabilities().getCapability("digitalai:reportTestId");
            motDriver.quit();
            try {
                Thread.sleep(60000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            ObservatoryUrlManager.getAttachmentsByTestID(testId);
            motDriver = null;
        }

        if (techTomLauncherDriver != null) {
            Object testId = techTomLauncherDriver.getCapabilities().getCapability("digitalai:reportTestId");
            techTomLauncherDriver.quit();
            try {
                Thread.sleep(90000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            ObservatoryUrlManager.getAttachmentsByTestID(testId);
            techTomLauncherDriver = null;
        }
    }

    private static AndroidDriver createTechTomDriver(String language, String locale, String testName) throws Exception {
        DesiredCapabilities capabilities = new DesiredCapabilities();
        URL url = new URI("https://tscloud.vodafone.com/wd/hub").toURL();
        capabilities.setCapability("accessKey",
                System.getProperty("tscloud.accessKey"));
        capabilities.setCapability("appium:app",
                "cloud:uniqueName=" + System.getProperty("tscloud.appUniqueName"));
        capabilities.setCapability("appium:automationName", "flutter");
        capabilities.setCapability("platformName", "android");
        if (Objects.equals(System.getProperty("tscloud.appUniqueName"), "TechTomAutomationgermany")) {
            capabilities.setCapability("appium:appPackage", "com.vodafone.gigaAnalyzer.uat");
            capabilities.setCapability("appium:appActivity", "com.vodafone.gigaAnalyzer.MainActivity");
        } else {
            capabilities.setCapability("appium:appPackage", "com.vodafone.techtom.tr.uat");
            capabilities.setCapability("appium:appActivity", "com.vodafone.techtom.tr.MainActivity");
        }
        capabilities.setCapability("appium:udid", System.getProperty("tscloud.deviceId"));
        capabilities.setCapability("noReset", true);
        capabilities.setCapability("appium:observatoryWsUri", ObservatoryUrlManager.getObservatoryUrl());
        capabilities.setCapability("appium:skipPortForward", false);
        capabilities.setCapability("digitalai:testName", testName);
        capabilities.setCapability("appium:language", language);
        capabilities.setCapability("appium:locale", locale);
        capabilities.setCapability("newCommandTimeout", 1800);

        return new AndroidDriver(url, capabilities);
    }

    private static AndroidDriver createTechTomLauncherDriver(String language, String locale) throws Exception {
        DesiredCapabilities capabilities = new DesiredCapabilities();
        URL url = new URI("https://tscloud.vodafone.com/wd/hub").toURL();
        capabilities.setCapability("accessKey",
                System.getProperty("tscloud.accessKey"));
        capabilities.setCapability("appium:app",
                "cloud:uniqueName=TurkeyLauncher");
        capabilities.setCapability("appium:automationName", "flutter");
        capabilities.setCapability("platformName", "android");
        capabilities.setCapability("appium:appPackage", "com.example.techtom_launcher");
        capabilities.setCapability("appium:appActivity", ".MainActivity");
        capabilities.setCapability("appium:udid", System.getProperty("tscloud.deviceId"));
        capabilities.setCapability("appium:language", language);
        capabilities.setCapability("appium:locale", locale);
        capabilities.setCapability("digitalai:testName", "Launch TechTOM from launcher");
        capabilities.setCapability("otherApps",
                List.of("cloud:uniqueName=" + System.getProperty("tscloud.appUniqueName")));

        return new AndroidDriver(url, capabilities);
    }

    private static AndroidDriver createDelphiDriver(String language, String locale) throws Exception {
        DesiredCapabilities capabilities = new DesiredCapabilities();
        URL url = new URI("https://tscloud.vodafone.com/wd/hub").toURL();
        capabilities.setCapability("accessKey",
                System.getProperty("tscloud.accessKey"));
        capabilities.setCapability("appium:app",
                "cloud:uniqueName=Delphi");
        capabilities.setCapability("appium:automationName", "UIAutomator2");
        capabilities.setCapability("platformName", "android");
        capabilities.setCapability("appium:appPackage", "de.kabeldeutschland.delphi.mobile.test");
        capabilities.setCapability("appium:appActivity", "com.getcapacitor.BridgeActivity");
        capabilities.setCapability("appium:udid", System.getProperty("tscloud.deviceId"));
        capabilities.setCapability("otherApps",
                List.of("cloud:uniqueName=" + System.getProperty("tscloud.appUniqueName")));
        capabilities.setCapability("digitalai:testName", "Launch TechTOM from Delphi");
        capabilities.setCapability("appium:language", language);
        capabilities.setCapability("appium:locale", locale);

        return new AndroidDriver(url, capabilities);
    }

    private static AndroidDriver createMotDriver(String language, String locale) throws Exception {
        DesiredCapabilities capabilities = new DesiredCapabilities();
        URL url = new URI("https://tscloud.vodafone.com/wd/hub").toURL();
        capabilities.setCapability("accessKey",
                System.getProperty("tscloud.accessKey"));
        capabilities.setCapability("appium:app",
                "cloud:uniqueName=MOT");
        capabilities.setCapability("appium:automationName", "UIAutomator2");
        capabilities.setCapability("platformName", "android");
        capabilities.setCapability("appium:appPackage", "mLogistics.UM_Droid");
        capabilities.setCapability("appium:appActivity", "crc64520c4c846fe090aa.UmActivity");
        capabilities.setCapability("appium:udid", System.getProperty("tscloud.deviceId"));
        capabilities.setCapability("otherApps",
                List.of("cloud:uniqueName=" + System.getProperty("tscloud.appUniqueName")));
        capabilities.setCapability("digitalai:testName", "Launch TechTOM from MOT");
        capabilities.setCapability("newCommandTimeout", 300);
        capabilities.setCapability("appium:language", language);
        capabilities.setCapability("appium:locale", locale);

        return new AndroidDriver(url, capabilities);
    }
}
