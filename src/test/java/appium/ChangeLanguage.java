package appium;

import io.appium.java_client.android.AndroidDriver;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Parameters;
import org.testng.annotations.Test;

import java.util.Objects;

import pro.truongsinh.appium_flutter.FlutterFinder;
import utils.TestUtils;

public class ChangeLanguage {
    private FlutterFinder find;
    private AndroidDriver driver;

    @BeforeClass
    @Parameters({ "language", "locale", "testName" })
    public void setup(String language, String locale, String testName) throws Exception {
        driver = DriverManager.getTechTomDriver(language, locale, testName);
        find = new FlutterFinder(driver);
    }

    @Test
    public void changeLanguageTest() throws InterruptedException {
        // Go to Information Screen and change language
        TestUtils.clickToElement(find.byType("IconButton"), driver);
        if (Objects.equals(System.getProperty("tscloud.appUniqueName"), "TechTomAutomationgermany")) {
            TestUtils.clickToElement(find.text("App-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"), driver);
            TestUtils.clickToElement(find.text("Englisch"), driver);
            TestUtils.clickToElement(find.text("Anwenden"), driver);
        } else {
            TestUtils.clickToElement(find.text("Uygulama dil seçimi"), driver);
                TestUtils.clickToElement(find.text("Ingilizce"), driver);
            TestUtils.clickToElement(find.text("Uygula"), driver);
        }


        // Check for content in English
        driver.executeScript("flutter:waitFor", find.text("Customer type"), 2000);
        Thread.sleep(4000);
    }
}
