name: TechTOM Turkey UAT Appium Tests - Lambda

defaults:
  run:
    shell: bash -leo pipefail {0}
on:
  workflow_call:

jobs:
  steps:
    name: Update Automation Lambda
    runs-on: [ self-hosted, dev ]
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          fetch-depth: 0

      - name: Check for changes in Lambda
        id: lambda_changes
        run: |
          # Compare HEAD with previous commit in the same branch 
          #git fetch --no-tags
          #if git diff --quiet HEAD~1 HEAD -- aws/lambda/src/; then 
          # Compare with Main
          git fetch origin
          if git diff --quiet origin/main...HEAD -- aws/lambda/src/; then
            echo "No change in lambda"
            echo "changed=false" >> $GITHUB_OUTPUT
          else
            echo "Changes detected in lambda"
            echo "changed=true" >> $GITHUB_OUTPUT
          fi

      - name: Zip Code
        if: steps.lambda_changes.outputs.changed == 'true'
        uses: VFDE-ADAC/mirror-montudor-action-zip@v1
        with:
          args: zip -qq -r techTomTestAutomation.zip ./aws/lambda/src

      - name: Upload Code
        if: steps.lambda_changes.outputs.changed == 'true'
        run: |
          ls -altr
          aws lambda update-function-code \
          --function-name  techTomTestAutomation \
          --region eu-central-1 \
          --zip-file fileb://techTomTestAutomation.zip