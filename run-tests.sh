#!/bin/bash
set -e
source .env
gradle wrapper
# If we want to run multiple suites we can just give all of them as argument
# Ex:
# ./gradlew clean build changeLanguageFlow.xml technicianTipsFlow.xml -Ptscloud.accessKey="${TS_CLOUD_ACCESS_KEY}"
./gradlew clean build wifiCoverageCheckFlow.xml -Ptscloud.accessKey="${TS_CLOUD_ACCESS_KEY}" -Ptscloud.deviceId="${TS_CLOUD_DEVICE_ID}" -Ptscloud.appUniqueName="${TS_CLOUD_APP_UNIQUE_NAME}" -PlauncherApp="${LAUNCHER_APP}"
