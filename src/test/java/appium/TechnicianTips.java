package appium;

import io.appium.java_client.android.AndroidDriver;

import org.testng.annotations.*;
import pro.truongsinh.appium_flutter.FlutterFinder;
import utils.TestUtils;

import java.util.HashMap;

public class TechnicianTips {
        private FlutterFinder find;
        private AndroidDriver driver;

        @BeforeClass
        @Parameters({ "language", "locale", "testName" })
        public void setup(String language, String locale, String testName) throws Exception {
                driver = DriverManager.getTechTomDriver(language, locale, testName);
                find = new FlutterFinder(driver);
        }

        @Test
        public void technicianTipsTest() throws InterruptedException {
                // Go to Test & Reports Screen
                TestUtils.clickToElement(find.byValueKey("_NavigationBar_Tab1_"), driver);
                driver.executeScript("flutter:scrollUntilVisible", find.byType("SingleChildScrollView"),
                                new HashMap<String, Object>() {
                                        {
                                                put("item", find.byValueKey(
                                                                "_TechnicianTipsConsultationConsent_Button_"));
                                                put("dxScroll", 90);
                                                put("dyScroll", -400);
                                        }
                                });

                // Start Technician Tips flow
                TestUtils.clickToElement(find.byValueKey("_TechnicianTipsConsultationConsent_Button_"), driver);

                // Fill tuning Internet & Telefon form
                TestUtils.clickToElement(
                                find.byValueKey("_IndicateAllThatApplyForInternet&Telefon_GigaCustomer_UnselectedOption_"),
                                driver);

                // Check if state is persistent
                checkStatePersistency();
                TestUtils.clickToElement(find.text("Next"), driver);

                // Fill tuning TV
                TestUtils.clickToElement(
                                find.byValueKey("_IndicateAllThatApplyForTv_GigatvCableIncludingHdPremium_UnselectedOption_"),
                                driver);
                TestUtils.clickToElement(find.text("Next"), driver);

                // Check edit values functionality
                editValues();

                // Fill customer confirmation form
                String launcherApp = System.getProperty("launcherApp");
                if (launcherApp.equals("MOT")) {
                        TestUtils.clickToElement(
                                        find.byValueKey("_Giga_report_custconfirmation_contact_vfwest_contacted_consent_UnselectedOption_"),
                                        driver);
                } else {
                        TestUtils.clickToElement(
                                        find.byValueKey("_Giga_report_custconfirmation_contact_vkd_contacted_consent_UnselectedOption_"),
                                        driver);
                }
                TestUtils.clickToElement(
                                find.byValueKey("_Giga_report_custconfirmation_morn_text_UnselectedOption_"),
                                driver);
                TestUtils.clickToElement(find.text("Next"), driver);

                // Fill signature
                TestUtils.drawSignature(driver);
                Thread.sleep(6000);
                TestUtils.clickToElement(find.text("Send report"), driver);

                // Wait for report to be sent and ready
                Thread.sleep(12000);

                // Dismiss bottom sheet for choosing the app in which to view the report
                TestUtils.dismissSystemBottomSheet(driver);
                Thread.sleep(7000);

                // Check that success overlay has been displayed
                driver.executeScript("flutter:waitFor", find.text(
                                "The report was successfully sent. Please inform the customer that the report is available on the MeinVodafone-Portal."),
                                12000);

                // Close success overlay
                TestUtils.clickToElement(find.text("I've informed the customer"), driver);
                Thread.sleep(3000);
        }

        private void editValues() throws InterruptedException {
                // Open edit tuning TV form
                TestUtils.scrollDownUntilElementIsVisible(find.text("Edit optimization options TV"), driver);
                TestUtils.clickToElement(find.text("Edit optimization options TV"), driver);

                // Edit value
                TestUtils.clickToElement(
                                find.byValueKey("_IndicateAllThatApplyForTv_GigatvCableIncludingHdPremium_SelectedOption_"),
                                driver);
                TestUtils.clickToElement(find.byValueKey("_IndicateAllThatApplyForTv_GigatvCable_UnselectedOption_"),
                                driver);

                // Navigate back to tuning TV form
                TestUtils.clickToElement(find.byValueKey("_BackButton_"), driver);
                TestUtils.clickToElement(find.text("Previous"), driver);

                // Check that the value has been updated
                Thread.sleep(3000);
                driver.executeScript("flutter:waitForTappable",
                                find.byValueKey("_IndicateAllThatApplyForTv_GigatvCable_SelectedOption_"));

                // Navigate back to edit values screen
                TestUtils.clickToElement(find.text("Next"), driver);
                TestUtils.clickToElement(find.text("Next"), driver);
        }

        private void checkStatePersistency() throws InterruptedException {
                // Tap on close button
                TestUtils.clickToElement(find.byValueKey("_CloseButton_"), driver);

                // Tap on continue button
                TestUtils.clickToElement(find.byValueKey("_TechnicianTipsConsultationConsent_Button_"), driver);

                // Check that the previously selected value is having the same state
                Thread.sleep(3000);
                driver.executeScript("flutter:waitForTappable",
                                find.byValueKey("_IndicateAllThatApplyForInternet&Telefon_GigaCustomer_SelectedOption_"));
        }
}
