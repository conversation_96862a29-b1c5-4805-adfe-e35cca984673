package appium;

import com.google.common.collect.ImmutableMap;

import org.apache.http.annotation.Immutable;
import org.testng.Assert;
import org.testng.annotations.*;
import io.appium.java_client.android.AndroidDriver;
import pro.truongsinh.appium_flutter.FlutterFinder;
import utils.TestUtils;

public class SpeedTest {
    private FlutterFinder find;
    private AndroidDriver driver;

    @BeforeClass
    @Parameters({ "language", "locale", "testName" })
    public void setup(String language, String locale, String testName) throws Exception {
        driver = DriverManager.getTechTomDriver(language, locale, testName);
        find = new FlutterFinder(driver);
    }

    @Test
    public void speedTestSdkTest() throws InterruptedException {
        // Go to Wi-Fi Tools Screen
        TestUtils.clickToElement(find.byValueKey("_NavigationBar_Tab2_"), driver);

        // Tap on speed test button
        TestUtils.clickToElement(find.byValue<PERSON>ey("_Speedtest_Button_"), driver);

        // Tap on Start scan button
        TestUtils.clickToElement(find.text("Start scan"), driver);

        // wait for the scan results to display
        driver.executeScript("flutter:waitFor", find.text("Scan again"), 150000);

        Thread.sleep(2000);

        // Check that Value N/A is displayed
        try {
            driver.executeScript("flutter:waitForAbsent", find.byValueKey("_Value_NA_"), 8000);
            System.out.println("Value N/A is not displayed");
        } catch (Exception e) {
            // Value N/Ais displayed
            Assert.fail();
        }

    }
}
