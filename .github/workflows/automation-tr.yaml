name: TechTOM Turkey UAT Appium Tests

on:
  push:
    branches:
      - adacga-2762
  #schedule:
  #  - cron: '00 21 * * *'
  workflow_dispatch:

env:
  TS_CLOUD_ACCESS_KEY: ${{ secrets.TS_CLOUD_ACCESS_KEY }}
  TS_CLOUD_DEVICE_ID: ${{ vars.TS_CLOUD_DEVICE_ID }}
  TS_CLOUD_APP_UNIQUE_NAME: ${{ vars.TS_CLOUD_TR_APP_UNIQUE_NAME }}
  LAUNCHER_APP: ${{ vars.LAUNCHER_APP }}


jobs:

  lambda-update:
    uses: ./.github/workflows/automation-lambda.yaml

  automated-test:
    name: Running Test Automation on TS Cloud
    needs: lambda-update
    runs-on: [ self-hosted,  preprod ]
    outputs:
      change_lang: ${{ steps.collect.outputs.change_lang }}
      feedback: ${{ steps.collect.outputs.feedback }}
      wifi_coverage: ${{ steps.collect.outputs.wifi_coverage }}
      any_fail: ${{ steps.collect.outputs.any_fail }}
    steps:
      - uses: actions/checkout@v3

      - name: Setup Gradle Wrapper
        run: |
          mkdir -p gradle/wrapper
          curl -L -o gradle/wrapper/gradle-wrapper.jar "https://github.com/gradle/gradle/raw/master/gradle/wrapper/gradle-wrapper.jar"
          chmod +x gradlew

      - id: change_lang
        name: Execute Change Language Test
        continue-on-error: true
        run: |
          set +e
          ./gradlew clean build changeLanguageTurkeyFlow.xml -Ptscloud.accessKey="${TS_CLOUD_ACCESS_KEY}" -Ptscloud.deviceId="${TS_CLOUD_DEVICE_ID}" -Ptscloud.appUniqueName="${TS_CLOUD_APP_UNIQUE_NAME}" -PlauncherApp="${LAUNCHER_APP}"
          rc=$?
          [ $rc -eq 0 ] && r=success || r=failure
          echo "CHANGE_LANG=$r" >> results.env

      - id: feedback
        name: Execute Feedback Test
        continue-on-error: true
        run: |
          set +e
          ./gradlew clean build provideFeedbackTurkeyFlow.xml -Ptscloud.accessKey="${TS_CLOUD_ACCESS_KEY}" -Ptscloud.deviceId="${TS_CLOUD_DEVICE_ID}" -Ptscloud.appUniqueName="${TS_CLOUD_APP_UNIQUE_NAME}" -PlauncherApp="${LAUNCHER_APP}"
          rc=$?
          [ $rc -eq 0 ] && r=success || r=failure
          echo "FEEDBACK=$r" >> results.env

      - id: wifi_coverage
        name: Execute WiFi Coverage Test
        run: |
          set +e
          ./gradlew clean build wifiCoverageTurkeyFlow.xml -Ptscloud.accessKey="${TS_CLOUD_ACCESS_KEY}" -Ptscloud.deviceId="${TS_CLOUD_DEVICE_ID}" -Ptscloud.appUniqueName="${TS_CLOUD_TR_APP_UNIQUE_NAME}" -PlauncherApp="${LAUNCHER_APP}"
          rc=$?
          [ $rc -eq 0 ] && r=success || r=failure
          echo "WIFI_COVERAGE=$r" >> results.env

      - id: collect
        name: Collect outcomes
        if: ${{ always() }}
        run: |
          # default to "skipped" if any key is missing
          [ -f results.env ] && source results.env || true
          echo "change_lang=${CHANGE_LANG:-skipped}"        >> "$GITHUB_OUTPUT"
          echo "feedback=${FEEDBACK:-skipped}"              >> "$GITHUB_OUTPUT"
          echo "wifi_coverage=${WIFI_COVERAGE:-skipped}"    >> "$GITHUB_OUTPUT"
          fail_count=0
          for v in "$CHANGE_LANG" "$FEEDBACK" "$WIFI_COVERAGE"; do
            [ "$v" = "failure" ] && fail_count=$((fail_count+1))
            done
          [ $fail_count -gt 0 ] && any_fail=yes || any_fail=no
          echo "any_fail=$any_fail" >> "$GITHUB_OUTPUT"
      - name: Set overall status
        if: ${{ steps.collect.outputs.any_fail == 'yes' }}
        shell: bash
        run: |
          echo "One or more tests failed."
          exit 1

  format-email:
    name: Format email
    needs: automated-test
    runs-on: [ self-hosted, preprod ]
    if: ${{ always() }}
    env:
      FROM_EMAIL: ${{ vars.TT_SES_FROM_EMAIL_AUTOMATION_NOTIFICATION }}
      #TO_EMAILS: ${{ vars.TT_TR_TO_EMAILS_AUTOMATION_NOTIFICATION }}
      TO_EMAILS: ${{ vars.TT_TEST_SUBJECT }}

    outputs:
      FROM_EMAIL: ${{ steps.ses.outputs.FROM_EMAIL }}
      DESTINATION: ${{ steps.ses.outputs.DESTINATION }}
      CONTENT: ${{ steps.ses.outputs.CONTENT }}

    steps:
      - name: Set environment variables
        run: |
          echo "CHANGE_LANG=${{ needs.automated-test.outputs.change_lang }}" >> $GITHUB_ENV
          echo "FEEDBACK=${{ needs.automated-test.outputs.feedback }}" >> $GITHUB_ENV
          echo "WIFI_COVERAGE=${{ needs.automated-test.outputs.wifi_coverage }}" >> $GITHUB_ENV

      - name: Build HTML email
        id: msg
        run: |
          chmod +x .github/workflows/scripts/build-email-turkey.sh
          .github/workflows/scripts/build-email-turkey.sh .github/workflows/email-templates/notification_email_turkey.html > full_output.txt
          
          SUBJECT=$(grep '^SUBJECT=' full_output.txt | cut -d'=' -f2-)
          echo "SUBJECT=$SUBJECT" >> $GITHUB_OUTPUT
          
          # Create clean HTML body (without SUBJECT line)
          grep -v '^SUBJECT=' full_output.txt > body.html
          
          # Escape HTML for SES JSON
          HTML_READY=$(jq -Rs . < body.html)
          echo "HTML_READY=$HTML_READY" >> $GITHUB_OUTPUT

      - name: Build SES payload
        id: ses
        shell: bash
        env:
          SUBJECT: ${{ steps.msg.outputs.SUBJECT }}
          HTML_ESCAPED: ${{ steps.msg.outputs.HTML_READY }}
        run: |
          # DESTINATION: wrap the JSON array in the SES shape
          printf '%s' "$TO_EMAILS" | jq '{ToAddresses: .}' > dest.json
          
          # CONTENT: full SES "Simple" object (let jq handle escaping)
          jq -n --arg subject "$SUBJECT" --argjson html "$HTML_ESCAPED" \
            '{Simple:{Subject:{Data:$subject},Body:{Html:{Data:$html}}}}' > content.json
          
          # Publish job outputs (compact JSON, single-line)
          echo "FROM_EMAIL=$FROM_EMAIL"                         >> "$GITHUB_OUTPUT"
          echo "DESTINATION=$(jq -c . < dest.json)"             >> "$GITHUB_OUTPUT"
          echo "CONTENT=$(jq -c . < content.json)"              >> "$GITHUB_OUTPUT"

  notify:
    name: Notify Stakeholders
    uses: VFDE-ADAC/shared-github-workflow/.github/workflows/ses.yaml@main
    needs: format-email
    if: ${{ always() }}
    with:
      AWS_REGION: eu-central-1
      FROM_EMAIL: ${{ needs.format-email.outputs.FROM_EMAIL }}
      DESTINATION: ${{ needs.format-email.outputs.DESTINATION }}
      CONTENT: ${{ needs.format-email.outputs.CONTENT }}
      RUNNER_TAG: preprod