package appium;



import org.openqa.selenium.By;

import org.openqa.selenium.JavascriptExecutor;

import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;

import org.testng.Assert;
import org.testng.annotations.*;
import org.testng.annotations.Parameters;

import java.time.Duration;
import java.util.List;
import java.util.Set;

import io.appium.java_client.android.AndroidDriver;

import pro.truongsinh.appium_flutter.FlutterFinder;

import utils.TestUtils;

public class WiFiCoverageTurkey {
    private FlutterFinder find;
    private AndroidDriver driver;

    @BeforeClass
    @Parameters({"language", "locale", "testName"})
    public void setup(String language, String locale, String testName) throws Exception {
        driver = DriverManager.getTechTomDriver(language, locale, testName);
        find = new FlutterFinder(driver);
    }

    @Test
    public  void wifiCoverageTrTest() throws InterruptedException{

        //Go to Test & Reports Screen
        TestUtils.clickToElement(find.byValueKey("_Tests&Reports_Tab_"), driver);

        //Start WiFi Coverage Flow
        TestUtils.clickToElement(find.byValueKey("_WiFiCoverageCheck_Button_"), driver);

        //Start Coverage Test
        TestUtils.clickToElement(find.text("Start Coverage Test"), driver);

        //Start scan for the router
        TestUtils.clickToElement(find.text("Start scan"), driver);
        
        // Wait for app/webview to load
        Thread.sleep(5000);

        //Click on close button
        TestUtils.clickToElement(find.byValueKey("_CloseButton_"), driver);
        
        //Cancel the popup
        TestUtils.clickToElement(find.byValueKey("_Cancel_"), driver);

        startSpeedTest();

        // Collapse the item
        TestUtils.clickToElement(find.byValueKey("_ExpandOrCollapseRouter_Button_"), driver);

        // Proceed to improvements
        proceedToImprovements();

        // Perform improvement
        performImprovement();

        // Finalize report
        TestUtils.clickToElement(find.text("Finalize report"), driver);

        // Send report
        TestUtils.clickToElement(find.text("Send report"), driver);

        // Wait for report to be sent and ready
        Thread.sleep(12000);


    }

    private void startSpeedTest() throws InterruptedException {

        // First, ensure we're in Flutter context
        driver.context("FLUTTER");

        Thread.sleep(3000);

        Set<String> contexts;
        String webviewContext = null;
        int attempts = 0;

        // Retry logic for webview detection
        while (webviewContext == null && attempts < 15) { // Try for 15 seconds
            contexts = driver.getContextHandles();
            System.out.println("Available contexts: " + contexts);

            for (String ctx : contexts) {
                if (ctx.toLowerCase().contains("webview") && ctx.contains("techtom")) {
                    // Test if this webview is actually usable
                    try {
                        Thread.sleep(5000);
                        driver.context(ctx);
                        Thread.sleep(3000);
                        driver.getCurrentUrl(); // This will fail if webview is not ready
                        Thread.sleep(5000);
                        webviewContext = ctx;
                        System.out.println("Found usable webview: " + ctx);
                        break;
                    } catch (Exception e) {
                        System.out.println("Webview " + ctx + " exists but not ready");
                        driver.context("FLUTTER"); // Switch back
                    }
                }
            }

            if (webviewContext == null) {
                System.out.println("No usable webview found, waiting... attempt " + (attempts + 1));
                Thread.sleep(3000);
                attempts++;
            }
        }

        // Now proceed with the speed test
        //driver.context(webviewContext);
        System.out.println("Switched to webview: " + webviewContext);

        Thread.sleep(6000);

        //Click on GO button
        JavascriptExecutor js = driver;
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(120));
        WebElement goButton = wait.until(ExpectedConditions.elementToBeClickable(By.cssSelector("button.button.background-primary-hover.text-primary")));
        js.executeScript("arguments[0].click();", goButton);

        //Wait until results stabilize
        WebDriverWait waitResults = new WebDriverWait(driver, Duration.ofSeconds(120));
        waitResults.until(d -> {
            try {
                List<WebElement> element = d.findElements(By.cssSelector("div.number.monochrome-primary"));
                if (element.size() >= 2) {
                    String d1 = element.get(0).getText().trim();
                    String u1 = element.get(1).getText().trim();

                    // ignore empty values
                    if (!d1.isEmpty() && !u1.isEmpty()) {
                        try { Thread.sleep(3000); } catch (InterruptedException e) {}

                        // Refetch elements to avoid stale element reference
                        List<WebElement> freshElements = d.findElements(By.cssSelector("div.number.monochrome-primary"));
                        if (freshElements.size() >= 2) {
                            String d2 = freshElements.get(0).getText().trim();
                            String u2 = freshElements.get(1).getText().trim();

                            if (d1.equals(d2) && u1.equals(u2)) {
                                return new String[]{d2, u2}; // stable
                            }
                        }
                    }
                }
            } catch (Exception e) {
                // Handle stale element or other exceptions by continuing to wait
                System.out.println("Retrying");
            }
            return null; // keep waiting
        });

        // Wait a bit more to ensure the webview is completely done
        Thread.sleep(6000);

        // Switch back to Flutter
        driver.context("FLUTTER");

        //Click on close button
        TestUtils.clickToElement(find.byValueKey("_CloseButton_"), driver);

        // Wait for UI to stabilize after closing
        Thread.sleep(4000);

    }

    private void addRoom() throws InterruptedException {
        // Tap on add room button
        TestUtils.clickToElement(find.text("Add a room"), driver);

        // Select a room from the menu
        TestUtils.clickToElement(find.byValueKey("_Bedroom_MenuItem_"), driver);

        // Tap on next button
        TestUtils.clickToElement(find.text("Next"), driver);

        // Start scanning the new room
        TestUtils.clickToElement(find.text("Start scanning"), driver);

        // Wait for scanning to take place
        Thread.sleep(6000);
    }

    private void editRoomName() throws InterruptedException {
        // Tap on edit buttom for bedroom
        TestUtils.clickToElement(find.byValueKey("_EditRoomName_BedroomButton_"), driver);

        // Add an already existing name
        TestUtils.fillFlutterTextField(find.byValueKey("_RoomModal_TextField_"), driver, "Router");

        Thread.sleep(300);

        // Check that error message gets displayed
        driver.executeScript("flutter:waitFor", find.text("This is a duplicate, please write another name"), 2000);

        // Add a new name
        TestUtils.fillFlutterTextField(find.byValueKey("_RoomModal_TextField_"), driver, "Bedroom2");

        // Change name
        TestUtils.clickToElement(find.text("Change name"), driver);

        // Check that the name has beed updated
        driver.executeScript("flutter:waitFor", find.text("Bedroom2"), 2000);
    }

    private void proceedToImprovements() throws InterruptedException {
        // Tap on proceed to improvements button
        TestUtils.clickToElement(find.text("Proceed to improvements"), driver);

        // Check if we still are on Initial check screen, meaning there are not enough
        // rooms added then add one
        String initialCheckTitle = find.text("Initial check").getText();
        if (initialCheckTitle.equals("Initial check")) {
            // Add a room
            addRoom();

            // Add a speedtest for it
            startSpeedTest();

            // Test edit its name
            editRoomName();

            // Tap on proceed to improvements button
            Thread.sleep(5000);
            TestUtils.clickToElement(find.text("Proceed to improvements"), driver);
        }

        // Check that we landed on Improvement check screen
        driver.executeScript("flutter:waitFor", find.text("Improvement check"), 2000);
    }

    private void performImprovement() throws InterruptedException {
        // Tap on rescan button for the router
        Thread.sleep(2000);
        TestUtils.clickToElement(find.byValueKey("_Router_RescanCoverage_Button_"), driver);

        startSpeedTest();
        // Wait for rescan to happen
        driver.executeScript("flutter:waitFor", find.text("Improvement check"), 150000);

        // Check that the item is now expandable
        TestUtils.clickToElement(find.byValueKey("_ExpandOrCollapseRouter_Button_"), driver);

        // Check that for the router, both coverage and speedtest were rescaned
        // Store current values
        String currentDownloadSpeed = find.byValueKey("_DownloadSpeed_Router_").getText();
        String currentUploadSpeed = find.byValueKey("_UploadSpeed_Router_").getText();
        String currentStrengthValue = find.byValueKey("_Router_StrengthValue_").getText();

        // Navigate to previous values and store the values
        TestUtils.clickToElement(find.text("Before"), driver);
        TestUtils.clickToElement(find.byValueKey("_ExpandOrCollapseRouter_Button_"), driver);
        String oldDownloadSpeed = find.byValueKey("_DownloadSpeed_Router_").getText();
        String oldUploadSpeed = find.byValueKey("_UploadSpeed_Router_").getText();
        String oldStrengthValue = find.byValueKey("_Router_StrengthValue_").getText();

        // Check if there is a change in the values
        boolean isImprovement = false;
        if (!currentDownloadSpeed.equals(oldDownloadSpeed) || !currentUploadSpeed.equals(oldUploadSpeed)
                || !currentStrengthValue.equals(oldStrengthValue)) {
            isImprovement = true;
        }

        if (!isImprovement) {
            Assert.fail();
        }

        // Go back to current improvement screen
        TestUtils.clickToElement(find.text("Current"), driver);

        // Check rescan speedtest functionality
        TestUtils.clickToElement(find.byValueKey("_ExpandOrCollapseRouter_Button_"), driver);
        String oldDownload = find.byValueKey("_DownloadSpeed_Router_").getText();
        String oldUpload = find.byValueKey("_UploadSpeed_Router_").getText();

        TestUtils.clickToElement(find.byValueKey("_Router_RescanSpeedtest_Button_"), driver);

        startSpeedTest();

        // Wait for rescan to happen
        driver.executeScript("flutter:waitFor", find.text("Improvement check"), 150000);

        String currentDownload = find.byValueKey("_DownloadSpeed_Router_").getText();
        String currentUpload = find.byValueKey("_UploadSpeed_Router_").getText();

        if (currentDownload.equals(oldDownload) && currentUpload.equals(oldUpload)) {
            Assert.fail();
        }

        TestUtils.clickToElement(find.text("Continue to report"), driver);
    }




}