package appium;

import io.appium.java_client.android.AndroidDriver;

import org.testng.annotations.*;
import pro.truongsinh.appium_flutter.FlutterFinder;
import utils.TestUtils;

public class Onboarding {
    protected FlutterFinder find;
    protected AndroidDriver driver;

    @BeforeClass
    @Parameters({ "language", "locale", "testName" })
    public void setup(String language, String locale, String testName) throws Exception {
        driver = DriverManager.getTechTomDriver(language, locale, testName);
        find = new FlutterFinder(driver);
    }

    @Test
    public void OnboardingTest() throws InterruptedException {
        // Wait for first screen to show up
        Thread.sleep(5000);

        // Tap on next button for all onboarding screens
        TestUtils.clickToElement(find.byValue<PERSON>ey("_nextButton0_"), driver);
        TestUtils.clickToElement(find.byValue<PERSON>ey("_nextButton1_"), driver);
        TestUtils.clickToElement(find.byValue<PERSON>ey("_nextButton2_"), driver);
        TestUtils.clickToElement(find.byValue<PERSON><PERSON>("_nextButton3_"), driver);
        TestUtils.clickToElement(find.byValue<PERSON>ey("_nextButton4_"), driver);
        TestUtils.clickToElement(find.byValueKey("_nextButton5_"), driver);
        TestUtils.clickToElement(find.byValueKey("_nextButton6_"), driver);
        //TestUtils.clickToElement(find.byValueKey("_nextButton7_"), driver);

        // Wait to get navigated to home screen
        Thread.sleep(5000);
    }
}
