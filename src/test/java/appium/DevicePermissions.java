package appium;

import java.time.Duration;
import org.openqa.selenium.By;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.testng.annotations.*;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.remote.SupportsContextSwitching;
import pro.truongsinh.appium_flutter.FlutterFinder;
import utils.TestUtils;

public class DevicePermissions {
        private AndroidDriver driver;
        private FlutterFinder find;
        private WebDriverWait wait;

        @BeforeClass
        @Parameters({ "language", "locale", "testName" })
        public void setup(String language, String locale, String testName) throws Exception {
                driver = DriverManager.getTechTomDriver(language, locale, testName);
                find = new FlutterFinder(driver);
                wait = new WebDriverWait(driver,
                                Duration.ofSeconds(20));
        }

        @Test
        public void DevicePermissionsTest() throws InterruptedException {
                // Tap on let's go button
                TestUtils.clickToElement(find.byValueKey("_LetsGo_Button_"), driver);

                // Turn on switch for Network&Service improvement
                TestUtils.clickToElement(find.byValueKey("_NetworkAndServicePermission_"), driver);

                // Tap on continue button
                TestUtils.scrollDownUntilElementIsVisible(find.byValueKey("_continue_button_"), driver);
                TestUtils.clickToElement(find.byValueKey("_continue_button_"), driver);

                // Turn on switch for Location permission
                TestUtils.clickToElement(find.byValueKey("_Location_permission_title_"), driver);

                // Turn on switch for Phone permission
                TestUtils.clickToElement(find.byValueKey("_Phone_permission_title_"), driver);

                // Tap on Confirm selection & continue button
                TestUtils.scrollDownUntilElementIsVisible(find.byValueKey("_confirm_selection_button_"), driver);
                TestUtils.clickToElement(find.byValueKey("_confirm_selection_button_"), driver);

                // Switch context in order to be able to interact with the native overlay
                ((SupportsContextSwitching) driver).context("NATIVE_APP");

                // Accept foreground location permission
                wait.until(ExpectedConditions.elementToBeClickable(By.xpath(
                                "//*[@resource-id='com.android.permissioncontroller:id/permission_allow_foreground_only_button']")));
                driver.findElement(By.xpath(
                                "//*[@resource-id='com.android.permissioncontroller:id/permission_allow_foreground_only_button']"))
                                .click();

                // Wait for device settings to get opened
                Thread.sleep(3000);

                // Accept background location permission
                wait.until(ExpectedConditions.elementToBeClickable(By.xpath(
                                "//*[@resource-id='com.android.permissioncontroller:id/allow_always_radio_button']")));
                driver.findElement(By.xpath(
                                "//*[@resource-id='com.android.permissioncontroller:id/allow_always_radio_button']"))
                                .click();

                // Navigate back to TechTOM
                driver.findElement(By.xpath(
                                "//*[@class='android.widget.ImageButton']"))
                                .click();

                // Accept phone calls permission
                wait.until(ExpectedConditions.elementToBeClickable(By.xpath(
                                "//*[@resource-id='com.android.permissioncontroller:id/permission_allow_button']")));
                driver.findElement(By.xpath(
                                "//*[@resource-id='com.android.permissioncontroller:id/permission_allow_button']"))
                                .click();

                ((SupportsContextSwitching) driver).context("FLUTTER");

                // Tap on OK button
                TestUtils.clickToElement(find.byValueKey("_Ok_Button_"), driver);

                // Check if user has landed on home screen
                driver.executeScript("flutter:waitFor", find.byValueKey("_NavigationBar_Tab0_"), 2000);
        }
}
