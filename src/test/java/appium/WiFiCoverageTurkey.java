package appium;

import static pro.truongsinh.appium_flutter.finder._FinderRawMethods.byValueKey;

import org.openqa.selenium.By;
import org.openqa.selenium.Dimension;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.Point;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.testng.Reporter;
import org.testng.annotations.*;
import org.testng.annotations.Parameters;

import java.time.Duration;
import java.util.List;
import java.util.Set;

import io.appium.java_client.AppiumDriver;
import io.appium.java_client.TouchAction;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.touch.TapOptions;
import io.appium.java_client.touch.offset.PointOption;
import pro.truongsinh.appium_flutter.FlutterFinder;
import pro.truongsinh.appium_flutter.finder.FlutterElement;
import utils.TestUtils;

public class WiFiCoverageTurkey {
    private FlutterFinder find;
    private AndroidDriver driver;

    @BeforeClass
    @Parameters({"language", "locale", "testName"})
    public void setup(String language, String locale, String testName) throws Exception {
        driver = DriverManager.getTechTomDriver(language, locale, testName);
        find = new FlutterFinder(driver);
    }

    @Test
    public  void wifiCoverageTrTest() throws InterruptedException{

        //Go to Test & Reports Screen
        TestUtils.clickToElement(find.byValueKey("_Tests&Reports_Tab_"), driver);

        //Start WiFi Coverage Flow
        TestUtils.clickToElement(find.byValueKey("_WiFiCoverageCheck_Button_"), driver);

        //Start Coverage Test
        TestUtils.clickToElement(find.text("Start Coverage Test"), driver);

        //Start scan for the router
        TestUtils.clickToElement(find.text("Start scan"), driver);

        //
        // Wait for app/webview to load
        Thread.sleep(5000);

        //Click on close button
        TestUtils.clickToElement(find.byValueKey("_CloseButton_"), driver);
        TestUtils.clickToElement(find.byValueKey("_Cancel_"), driver);

        // List available contexts
        Set<String> contexts = driver.getContextHandles();
        // goes to Gradle problems-report.html
        System.out.println("Available contexts: " + contexts);

        driver.context("WEBVIEW_com.vodafone.techtom.tr.uat");
        //driver.findElement(By.cssSelector("#main-content > div.button__wrapper > div > button")).click();
        JavascriptExecutor js = (JavascriptExecutor) driver;
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(15));
        WebElement goButton = wait.until(ExpectedConditions.elementToBeClickable(By.cssSelector("button.button.background-primary-hover.text-primary")));

        //goButton.click();
        js.executeScript("arguments[0].click();", goButton);

        // --- Wait until results stabilize ---
        WebDriverWait waitResults = new WebDriverWait(driver, Duration.ofSeconds(120));

        List<WebElement> results = waitResults.until(d -> {
            List<WebElement> elems = d.findElements(By.cssSelector("div.number.monochrome-primary"));
            if (elems.size() >= 2) {
                String d1 = elems.get(0).getText().trim();
                String u1 = elems.get(1).getText().trim();

                // ignore empty values
                if (!d1.isEmpty() && !u1.isEmpty()) {
                    try { Thread.sleep(3000); } catch (InterruptedException e) {}
                    String d2 = elems.get(0).getText().trim();
                    String u2 = elems.get(1).getText().trim();

                    if (d1.equals(d2) && u1.equals(u2)) {
                        return elems; // stable
                    }
                }
            }
            return null; // keep waiting
        });

        String downloadSpeed = results.get(0).getText();
        String uploadSpeed   = results.get(1).getText();

        System.out.println("✅ Download Speed: " + downloadSpeed);
        System.out.println("✅ Upload Speed: " + uploadSpeed);

        // Wait a bit more to ensure the webview is completely done
        Thread.sleep(2000);

        // Switch back to Flutter with verification
        switchToFlutterContext();

        //Click on close button

        TestUtils.clickToElement(find.byValueKey("_CloseButton1_"), driver);


        // Tap on expand button for the router and check if speedtest is already added
        TestUtils.clickToElement(find.byValueKey("_ExpandOrCollapseRouter_Button_"), driver);
        driver.executeScript("flutter:waitFor", find.byValueKey("_DownloadSpeed_Router_"), 1000);
        driver.executeScript("flutter:waitFor", find.byValueKey("_UploadSpeed_Router_"), 1000);

        // Collapse the item
        TestUtils.clickToElement(find.byValueKey("_ExpandOrCollapseRouter_Button_"), driver);




    }

    public static String[] waitForSpeedTestResults(AppiumDriver driver) {
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(120));

        return wait.until(d -> {
            List<WebElement> elems = d.findElements(By.cssSelector("div.number.monochrome-primary"));

            if (elems.size() >= 2) {
                String d1 = elems.get(0).getText().trim();
                String u1 = elems.get(1).getText().trim();

                if (!d1.isEmpty() && !u1.isEmpty()) {
                    try { Thread.sleep(4000); } catch (InterruptedException ignored) {}

                    String d2 = elems.get(0).getText().trim();
                    String u2 = elems.get(1).getText().trim();

                    // values must be stable
                    if (d1.equals(d2) && u1.equals(u2)) {
                        // wait extra time for UI rendering (final numbers)
                        try { Thread.sleep(5000); } catch (InterruptedException ignored) {}
                        return new String[]{d2, u2};
                    }
                }
            }
            return null; // keep waiting
        });
    }

    private void switchToFlutterContext() throws InterruptedException {
        try {
            // List available contexts to debug
            Set<String> contexts = driver.getContextHandles();
            System.out.println("Available contexts before switching: " + contexts);

            // Switch to Flutter context
            driver.context("FLUTTER");

            // Verify the switch was successful
            String currentContext = driver.getContext();
            System.out.println("Current context after switch: " + currentContext);

            // Wait a moment for context to stabilize
            Thread.sleep(1000);

        } catch (Exception e) {
            System.err.println("Error switching to Flutter context: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }



}