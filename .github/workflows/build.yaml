name: TechTOM UAT Appium Tests

on:
  schedule:
    - cron: '00 19 * * *'
  workflow_dispatch:

env:
  TS_CLOUD_ACCESS_KEY: ${{ secrets.TS_CLOUD_ACCESS_KEY }}
  TS_CLOUD_DEVICE_ID: ${{ vars.TS_CLOUD_DEVICE_ID }}
  TS_CLOUD_APP_UNIQUE_NAME: ${{ vars.TS_CLOUD_APP_UNIQUE_NAME }}
  LAUNCHER_APP: ${{ vars.LAUNCHER_APP }}

jobs:
  build-app-store-lambda:
    name: Update App Store Lambda
    runs-on: [ self-hosted, preprod ]
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          fetch-depth: 0

      - name: Check for changes in Lambda
        id: lambda_changes
        run: |
          # Compare HEAD with previous commit in the same branch 
          #git fetch --no-tags
          #if git diff --quiet HEAD~1 HEAD -- aws/lambda/src/; then 
          # Compare with Main
          git fetch origin
          if git diff --quiet origin/main...HEAD -- aws/lambda/src/; then
            echo "No change in lambda"
            echo "changed=false" >> $GITHUB_OUTPUT
          else
            echo "Changes detected in lambda"
            echo "changed=true" >> $GITHUB_OUTPUT
          fi

      - name: Zip Code
        if: steps.lambda_changes.outputs.changed == 'true'
        uses: VFDE-ADAC/mirror-montudor-action-zip@v1
        with:
          args: zip -qq -r techTomTestAutomation.zip ./aws/lambda/src

      - name: Upload Code
        if: steps.lambda_changes.outputs.changed == 'true'
        run: |
          ls -altr
          aws lambda update-function-code \
          --function-name  techTomTestAutomation \
          --region eu-central-1 \
          --zip-file fileb://techTomTestAutomation.zip

  automated-test:
    name: Test Automation
    needs: build-app-store-lambda
    runs-on: [ self-hosted,  preprod ]
    steps:
    - uses: actions/checkout@v3

    - name: Setup Gradle Wrapper
      run: |
        mkdir -p gradle/wrapper
        curl -L -o gradle/wrapper/gradle-wrapper.jar "https://github.com/gradle/gradle/raw/master/gradle/wrapper/gradle-wrapper.jar"
        chmod +x gradlew
    - name: Execute Change Language Test
      run: ./gradlew clean build changeLanguageFlow.xml -Ptscloud.accessKey="${TS_CLOUD_ACCESS_KEY}" -Ptscloud.deviceId="${TS_CLOUD_DEVICE_ID}" -Ptscloud.appUniqueName="${TS_CLOUD_APP_UNIQUE_NAME}" -PlauncherApp="${LAUNCHER_APP}"
    - name: Execute Giga Check Test
      run: ./gradlew clean build gigaCheckFlow.xml -Ptscloud.accessKey="${TS_CLOUD_ACCESS_KEY}" -Ptscloud.deviceId="${TS_CLOUD_DEVICE_ID}" -Ptscloud.appUniqueName="${TS_CLOUD_APP_UNIQUE_NAME}" -PlauncherApp="${LAUNCHER_APP}"
      #continue-on-error: true
    - name: Execute Feedback Test
      run: ./gradlew clean build provideFeedbackFlow.xml -Ptscloud.accessKey="${TS_CLOUD_ACCESS_KEY}" -Ptscloud.deviceId="${TS_CLOUD_DEVICE_ID}" -Ptscloud.appUniqueName="${TS_CLOUD_APP_UNIQUE_NAME}" -PlauncherApp="${LAUNCHER_APP}"
      #continue-on-error: true
    - name: Execute WiFi Coverage Test
      run: ./gradlew clean build wifiCoverageCheckFlow.xml -Ptscloud.accessKey="${TS_CLOUD_ACCESS_KEY}" -Ptscloud.deviceId="${TS_CLOUD_DEVICE_ID}" -Ptscloud.appUniqueName="${TS_CLOUD_APP_UNIQUE_NAME}" -PlauncherApp="${LAUNCHER_APP}"
      #continue-on-error: true
    - name: Execute Technician Tips Test
      run: ./gradlew clean build technicianTipsFlow.xml -Ptscloud.accessKey="${TS_CLOUD_ACCESS_KEY}" -Ptscloud.deviceId="${TS_CLOUD_DEVICE_ID}" -Ptscloud.appUniqueName="${TS_CLOUD_APP_UNIQUE_NAME}" -PlauncherApp="${LAUNCHER_APP}"
      #continue-on-error: true
