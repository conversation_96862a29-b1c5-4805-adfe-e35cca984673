<!doctype html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="x-apple-disable-message-reformatting">
    <title>CI Results</title>
    <style>
        body { font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica,Arial,sans-serif; margin:0; padding:24px; background:#f6f8fa; }
        .card { max-width:720px; margin:auto; background:#ffffff; border-radius:12px; padding:24px; border:1px solid #e6e8eb; }
        h1 { margin:0 0 12px; font-size:20px; }
        .subtitle { color:#57606a; margin:0 0 16px; }
        .kpi { display:flex; gap:10px; margin-top:8px; }
        .kpi div { background:#f0f3f6; padding:8px 12px; border-radius:8px; font-weight:600; }
        table { width:100%; border-collapse:collapse; margin-top:12px; }
        th, td { text-align:left; padding:10px 12px; border-bottom:1px solid #e6e8eb; vertical-align:middle; }
        th { color:#57606a; font-weight:600; }
        .name { white-space:nowrap; }
        .chip { font-size:12px; text-transform:uppercase; letter-spacing:.02em; padding:4px 8px; border-radius:999px; display:inline-block; font-weight:700; }
        .chip--ok   { background:#e7f5ee; color:#0f7b46; }
        .chip--fail { background:#ffebea; color:#b32025; }
        .chip--warn { background:#fff7cc; color:#8a6a00; }
        .meta { margin-top:16px; font-size:13px; color:#57606a; }
        .meta a { color:#0969da; text-decoration:none; }
        code { background:#f0f3f6; padding:2px 6px; border-radius:6px; }
    </style>
</head>
<body>
<div class="card">
    <h1>TechTOM Germany Regression Test</h1>
    <div class="subtitle">Status summary for latest run.</div>

    <div class="kpi">
        <div>✅ Passed: ${PASS}</div>
        <div>❌ Failed: ${FAIL}</div>
    </div>

    <table role="presentation" aria-hidden="true">
        <thead>
        <tr><th>Check</th><th>Status</th></tr>
        </thead>
        <tbody>
        <tr><td class="name">${change_lang_emoji} Change Language</td><td>${change_lang_chip}</td></tr>
        <tr><td class="name">${giga_check_emoji} Service and Product Check (Internet)</td><td>${giga_check_chip}</td></tr>
        <tr><td class="name">${feedback_emoji} Feedback</td><td>${feedback_chip}</td></tr>
        <tr><td class="name">${wifi_coverage_emoji} WiFi Coverage</td><td>${wifi_coverage_chip}</td></tr>
        <tr><td class="name">${technician_tip_emoji} Technician Tips</td><td>${technician_tip_chip}</td></tr>
        <tr><td class="name">${speed_test_emoji} Speed Test</td><td>${speed_test_chip}</td></tr>
        <tr><td class="name">${giga_check_tv_emoji} Service and Product Check (TV)</td><td>${giga_check_tv_chip}</td></tr>
        <tr><td class="name">${giga_check_internet_tv_emoji} Service and Product Check (Internet and TV)</td><td>${giga_check_internet_tv_chip}</td></tr>
        <tr><td class="name">${giga_check_internet_third_party_emoji} Service and Product Check (Internet/Third Party)</td><td>${giga_check_internet_third_party_chip}</td></tr>
        <tr><td class="name">${giga_check_internet_tv_third_party_emoji} Service and Product Check (InternetTV/Third Party)</td><td>${giga_check_internet_tv_third_party_chip}</td></tr>
        </tbody>
    </table>

    <div class="meta">
        <div><strong>Workflow:</strong> ${GITHUB_WORKFLOW}</div>
        <div><strong>Run:</strong> <a href="${RUN_URL}">${RUN_URL}</a></div>
        <div><strong>Commit:</strong> <code>${GITHUB_SHA}</code></div>
        <div><strong>Ref:</strong> <code>${GITHUB_REF_NAME}</code></div>
        <div><strong>Repo:</strong> <code>${GITHUB_REPOSITORY}</code></div>
    </div>
</div>
</body>
</html>