name: TechTOM Turkey UAT Appium Tests

on:
  push:
    branches:
      - adacga-2762
    #schedule:
    #- cron: '00 21 * * *'
  workflow_dispatch:

env:
  TS_CLOUD_ACCESS_KEY: ${{ secrets.TS_CLOUD_ACCESS_KEY }}
  TS_CLOUD_DEVICE_ID: ${{ vars.TS_CLOUD_DEVICE_ID }}
  TS_CLOUD_APP_UNIQUE_NAME: ${{ vars.TS_CLOUD_TR_APP_UNIQUE_NAME }}
  #LAUNCHER_APP: ${{ vars.LAUNCHER_APP }}
  LAUNCHER_APP: TurkeyLauncher

jobs:

  lambda-update:
    uses: ./.github/workflows/automation_lambda.yaml

  automated-test:
    name: Test Automation
    needs: lambda-update
    runs-on: [ self-hosted,  dev ]
    outputs:
      wifi_coverage: ${{ steps.collect.outputs.wifi_coverage }}
      any_fail: ${{ steps.collect.outputs.any_fail }}
    steps:
      - uses: actions/checkout@v3

      - name: Setup Gradle Wrapper
        run: |
          mkdir -p gradle/wrapper
          curl -L -o gradle/wrapper/gradle-wrapper.jar "https://github.com/gradle/gradle/raw/master/gradle/wrapper/gradle-wrapper.jar"
          chmod +x gradlew

      - id: wifi_coverage
        name: Execute WiFi Coverage Test
        run: |
          set +e
          ./gradlew clean build wifiCoverageTurkeyFlow.xml -Ptscloud.accessKey="${TS_CLOUD_ACCESS_KEY}" -Ptscloud.deviceId="${TS_CLOUD_DEVICE_ID}" -Ptscloud.appUniqueName="${TS_CLOUD_TR_APP_UNIQUE_NAME}" -PlauncherApp="${LAUNCHER_APP}"
          rc=$?
          [ $rc -eq 0 ] && r=success || r=failure
          echo "WIFI_COVERAGE=$r" >> results.env

      - id: collect
        name: Collect outcomes
        if: ${{ always() }}
        run: |
          # default to "skipped" if any key is missing
          [ -f results.env ] && source results.env || true
          echo "wifi_coverage=${WIFI_COVERAGE:-skipped}"    >> "$GITHUB_OUTPUT"
          fail_count=0
          for v in "$WIFI_COVERAGE"; do
            [ "$v" = "failure" ] && fail_count=$((fail_count+1))
            done
          [ $fail_count -gt 0 ] && any_fail=yes || any_fail=no
          echo "any_fail=$any_fail" >> "$GITHUB_OUTPUT"
      - name: Set overall status
        if: ${{ steps.collect.outputs.any_fail == 'yes' }}
        shell: bash
        run: |
          echo "One or more tests failed."
          exit 1

  notify_email_via_ses:
    name: Notify via SES
    needs: automated-test
    runs-on: [ self-hosted, dev ]
    if: ${{ always() }}
    env:
      AWS_REGION: eu-central-1
      FROM_EMAIL: ${{ vars.TT_SES_FROM_EMAIL_AUTOMATION_NOTIFICATION }}
      TO_EMAILS: ${{ vars.TT_TR_TO_EMAILS_AUTOMATION_NOTIFICATION }}

    steps:
      - name: Build HTML email (envsubst; no heredocs)
        id: msg
        shell: bash
        env:
          WIFI_COVERAGE: ${{ needs.automated-test.outputs.wifi_coverage }}
          HTML_TEMPLATE: |-
            <!doctype html>
            <html>
              <head>
                <meta charset="utf-8" />
                <meta name="x-apple-disable-message-reformatting">
                <title>CI Results</title>
                <style>
                  body { font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica,Arial,sans-serif; margin:0; padding:24px; background:#f6f8fa; }
                  .card { max-width:720px; margin:auto; background:#ffffff; border-radius:12px; padding:24px; border:1px solid #e6e8eb; }
                  h1 { margin:0 0 12px; font-size:20px; }
                  .subtitle { color:#57606a; margin:0 0 16px; }
                  .kpi { display:flex; gap:10px; margin-top:8px; }
                  .kpi div { background:#f0f3f6; padding:8px 12px; border-radius:8px; font-weight:600; }
                  table { width:100%; border-collapse:collapse; margin-top:12px; }
                  th, td { text-align:left; padding:10px 12px; border-bottom:1px solid #e6e8eb; vertical-align:middle; }
                  th { color:#57606a; font-weight:600; }
                  .name { white-space:nowrap; }
                  .chip { font-size:12px; text-transform:uppercase; letter-spacing:.02em; padding:4px 8px; border-radius:999px; display:inline-block; font-weight:700; }
                  .chip--ok   { background:#e7f5ee; color:#0f7b46; }
                  .chip--fail { background:#ffebea; color:#b32025; }
                  .chip--warn { background:#fff7cc; color:#8a6a00; }
                  .meta { margin-top:16px; font-size:13px; color:#57606a; }
                  .meta a { color:#0969da; text-decoration:none; }
                  code { background:#f0f3f6; padding:2px 6px; border-radius:6px; }
                </style>
              </head>
              <body>
                <div class="card">
                  <h1>TechTOM Turkey Regression Test</h1>
                  <div class="subtitle">Status summary for latest run.</div>
                  <div class="kpi">
                    <div>✅ Passed: ${PASS}</div>
                    <div>❌ Failed: ${FAIL}</div>
                  </div>
                  <table role="presentation" aria-hidden="true">
                    <thead>
                      <tr><th>Check</th><th>Status</th></tr>
                    </thead>
                    <tbody>
                      <tr><td class="name">${WF_E} WiFi Coverage</td><td>${WF_C}</td></tr>
                    </tbody>
                  </table>
                  <div class="meta">
                    <div><strong>Workflow:</strong> ${GITHUB_WORKFLOW}</div>
                    <div><strong>Run:</strong> <a href="${RUN_URL}">${RUN_URL}</a></div>
                    <div><strong>Commit:</strong> <code>${GITHUB_SHA}</code></div>
                    <div><strong>Ref:</strong> <code>${GITHUB_REF_NAME}</code></div>
                    <div><strong>Repo:</strong> <code>${GITHUB_REPOSITORY}</code></div>
                  </div>
                </div>
              </body>
            </html>
        run: |
          # Helpers
          status_emoji () {
            case "$1" in
              success) echo "✅" ;;
              failure) echo "❌" ;;
              *)       echo "⚠️" ;;
            esac
          }
          status_chip () {
            case "$1" in
              success) echo '<span class="chip chip--ok">success</span>' ;;
              failure) echo '<span class="chip chip--fail">failure</span>' ;;
              *)       echo '<span class="chip chip--warn">unknown</span>' ;;
            esac
          }
          # Derive emojis/chips
          WF_E=$(status_emoji "$WIFI_COVERAGE");  WF_C=$(status_chip "$WIFI_COVERAGE")
          # Pass/fail counts
          PASS=$(printf "%s\n" \
             "$WIFI_COVERAGE" | grep -c '^success$' || true)
          FAIL=$(printf "%s\n" \
            "$WIFI_COVERAGE" | grep -c '^failure$' || true)
          SUBJECT="TechTOM Turkey Automation Results: ${PASS} passed, ${FAIL} failed"
          echo "SUBJECT=${SUBJECT}" >> "$GITHUB_OUTPUT"
          RUN_URL="${GITHUB_SERVER_URL}/${GITHUB_REPOSITORY}/actions/runs/${GITHUB_RUN_ID}"
          # Vars for template substitution
          export WF_E 
          export WF_C 
          export GITHUB_WORKFLOW GITHUB_REPOSITORY GITHUB_REF_NAME GITHUB_SHA RUN_URL
          export PASS FAIL
          # Render HTML
          printf '%s' "$HTML_TEMPLATE" > template.html
          envsubst '${WF_E} ${WF_C} ${GITHUB_WORKFLOW} ${RUN_URL} ${GITHUB_SHA} ${GITHUB_REF_NAME} ${GITHUB_REPOSITORY} ${PASS} ${FAIL}' \
            < template.html > body.html
          # Escape HTML for SES JSON
          echo "HTML_READY=$(jq -Rs . < body.html)" >> "$GITHUB_OUTPUT"
      - name: Send via Amazon SES
        shell: bash
        env:
          SUBJECT: ${{ steps.msg.outputs.SUBJECT }}
          HTML_ESCAPED: ${{ steps.msg.outputs.HTML_READY }}
        run: |
          echo "$TO_EMAILS" | jq '{ToAddresses: .}' > dest.json
          jq -n --arg subject "$SUBJECT" --argjson html "$HTML_ESCAPED" \
            '{Simple:{Subject:{Data:$subject},Body:{Html:{Data:$html}}}}' > content.json
          
          # Send
          aws sesv2 send-email \
            --region eu-central-1 \
            --from-email-address "$FROM_EMAIL" \
            --destination file://dest.json \
            --content file://content.json