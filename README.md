# Appium automation tests

### How to run the tests?

#### Cloud:
1. In the .env file update your access key to connect to Ts cloud, device id, app unique name and launcher app's unique name. 

- How to find the access key? 
    Login to ts cloud and click on your proile icon. You will find get access key option. Access key is unique for each user. (we will use the access key for techtom automation account)

- How to get device id?
    In Ts cloud, go to mobile devices section, change view mode to columns and then look for ID column.

- How to get app's unique name?
    Go to applications section and look for Unique Name column. Same for both TechTOM and launcher app (Delphi, MOT or TechTomLauncher)

2. Once all variables from .env file are setup, we need to configure run_tests script depending on what tests do we need to run.

- How can we run a single specific test?
    We need to add the coresponding xml file name as parameter to the ./gradlew command
    ex: ./gradlew clean build technicianTipsFlow.xml

- How can we run multiple flows?
    We just need to add the flows names into the run command:
    ex: ./gradlew clean build technicianTipsFlow.xml gigaCheckFlow.xml wifiCoverageCheckFlow.xml

#### Local:
1. For this we won't need the acces key and app unique names capabilities. The rest of capabilities, need to be kept. 
2. As devide id, you can use "adb devices" command and just take the device's id from the output.
3. Before starting the test script, we need to start a local appium server (appium --use-plugins=flutter).
4. Now as the appium session is started, we need to use it's url as capability for the driver to be able to connect to it.
5. Once the above steps are completed, just run the script.


### What steps to follow while implementing a new test flow?
1. Start with creating the java test file, it will contain the test script and all actions that will be executed on the mobile device during the test.
2. Create an xml file specific for that flow, it will contain all tests that need to be executed for that flow.
3. Once the xml file is created, the build.gradle will already create a task for it as it has a dynamic method that creates tasks for each xml file from resources folder.

### How to create build for TechTOM and TechTOM Launcher?
- Everytime we need to use as entrypoint the main.dart from automation testing folder as in contains an method that will enable the flutter driver.

#### Github actions Pipeline:
The tests can be executed via GitHub Actions.