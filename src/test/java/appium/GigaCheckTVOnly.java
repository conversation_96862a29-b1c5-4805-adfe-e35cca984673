package appium;

import org.testng.annotations.BeforeClass;
import org.testng.annotations.Parameters;
import org.testng.annotations.Test;

import io.appium.java_client.android.AndroidDriver;
import pro.truongsinh.appium_flutter.FlutterFinder;
import utils.TestUtils;

public class GigaCheckTVOnly {
    private FlutterFinder find;
    private AndroidDriver driver;

    @BeforeClass
    @Parameters({ "language", "locale", "testName" })
    public void setup(String language, String locale, String testName) throws Exception {
        driver = DriverManager.getTechTomDriver(language, locale, testName);
        find = new FlutterFinder(driver);
    }

    @Test
    public void gigaCheckTVOnlyTest() throws InterruptedException {
        // Go to Test & Reports Screen
        TestUtils.clickToElement(find.byValueKey("_NavigationBar_Tab1_"), driver);

        // Start GigaCheck flow
        TestUtils.clickToElement(find.byValueKey("_Gigacheck_Button_"), driver);

        // Fill coverage area form
        TestUtils.clickToElement(find.byValue<PERSON>ey("_HandoverPoint_Gigacheck_da_checked_value_UnselectedOption_"),
                driver);

        TestUtils.clickToElement(find.text("Next"), driver);

        // Fill living area form
        TestUtils.clickToElement(find.byValueKey("_BookedProducts_Gigacheck_livingarea_tv_only_UnselectedOption_"), driver);

        TestUtils.scrollDownUntilElementIsVisible(find.byValueKey("_MultimediaDose_Gigacheck_da_checked_value_UnselectedOption_"), driver);
        TestUtils.clickToElement(find.text("Next"), driver);

        // Check that error message gets displayed
        driver.executeScript("flutter:waitFor", find.text("Mandatory field hasn’t been filled!"), 2000);

        TestUtils.clickToElement(find.byValueKey("_MultimediaDose_Gigacheck_da_checked_value_UnselectedOption_"), driver);
        TestUtils.scrollDownUntilElementIsVisible(find.byValueKey("_ConnectionCable_Gigacheck_da_checked_value_UnselectedOption_"), driver);
        TestUtils.clickToElement(find.byValueKey("_ConnectionCable_Gigacheck_da_checked_value_UnselectedOption_"), driver);
        TestUtils.clickToElement(find.text("Next"), driver);

        //Fill Internet form
        TestUtils.fillFlutterTextField(find.byValueKey("_MaximumTechnicalAvailableBandwidth_TextField_"), driver, "50");
        TestUtils.clickToElement(find.byValueKey("_Internet_Service_Provider_Dropdown_"), driver);
        TestUtils.clickToElement(find.text("Telekom"), driver);
        TestUtils.fillFlutterTextField(find.byValueKey("_BookedBandwidth_TextField_"), driver, "40");
        TestUtils.clickToElement(find.text("Next"), driver);

        //Fill TV form
        TestUtils.clickToElement(find.text("Next"), driver);
        driver.executeScript("flutter:waitFor", find.text("Mandatory field hasn’t been filled!"), 2000);
        TestUtils.clickToElement(find.byValueKey("_TvStatus_Giga_report_tvoption1_UnselectedOption_"), driver);
        TestUtils.clickToElement(find.text("Next"), driver);

        // Fill tuning Internet & Telefon form
        TestUtils.clickToElement(find.byValueKey("_IndicateAllThatApplyForInternet&Telefon_GigaCustomer_UnselectedOption_"), driver);
        TestUtils.clickToElement(find.text("Next"), driver);

        // Fill tuning TV
        TestUtils.clickToElement(find.byValueKey("_IndicateAllThatApplyForTv_GigatvCableIncludingHdPremium_UnselectedOption_"), driver);
        TestUtils.clickToElement(find.text("Next"), driver);

        // Check edit values functionality
        editValues();

        // Fill customer confirmation form
        String launcherApp = System.getProperty("launcherApp");
        if (launcherApp.equals("MOT")) {
            TestUtils.clickToElement(
                    find.byValueKey("_Giga_report_custconfirmation_contact_vfwest_contacted_consent_UnselectedOption_"),
                    driver);
        } else {
            TestUtils.clickToElement(
                    find.byValueKey("_Giga_report_custconfirmation_contact_vkd_contacted_consent_UnselectedOption_"),
                    driver);
        }
        TestUtils.clickToElement(
                find.byValueKey("_Giga_report_custconfirmation_morn_text_UnselectedOption_"),
                driver);
        TestUtils.clickToElement(find.text("Next"), driver);

        // Fill signature
        TestUtils.drawSignature(driver);
        Thread.sleep(6000);
        TestUtils.clickToElement(find.text("Send report"), driver);

        // Wait for report to be sent and ready
        Thread.sleep(12000);

        // Dismiss bottom sheet for choosing the app in which to view the report
        TestUtils.dismissSystemBottomSheet(driver);
        Thread.sleep(7000);

        // Check that success overlay has been displayed
        driver.executeScript("flutter:waitFor", find.text(
                        "The report was successfully sent. Please inform the customer that the report is available on the MeinVodafone-Portal."),
                12000);

        // Close success overlay
        TestUtils.clickToElement(find.text("I've informed the customer"), driver);
        Thread.sleep(3000);
    }
    private void editValues() throws InterruptedException {
        // Open edit TV form
        TestUtils.scrollDownUntilElementIsVisible(find.text("Edit TV"), driver);
        TestUtils.clickToElement(find.text("Edit TV"), driver);

        // Deselect mandatory field
        TestUtils.clickToElement(find.byValueKey("_TvStatus_Giga_report_tvoption1_SelectedOption_"), driver);

        // Check that error message is showing up in case of no selected option for
        // mandatory field
        driver.executeScript("flutter:waitFor", find.text("Mandatory field hasn’t been filled!"));

        TestUtils.clickToElement(find.byValueKey("_BackButton_"), driver);

        // Check that in case we navigate back and no selected option for mandatory
        // field, we see the error overlay
        driver.executeScript("flutter:waitFor", find.text("Fields are missing!"), 2000);
        TestUtils.clickToElement(find.text("Continue editing"), driver);

        // Edit value
        TestUtils.clickToElement(find.byValueKey("_TvStatus_Giga_report_tvoption2_UnselectedOption_"), driver);

        // Navigate back to TV form
        TestUtils.clickToElement(find.byValueKey("_BackButton_"), driver);
        TestUtils.clickToElement(find.text("Previous"), driver);
        TestUtils.clickToElement(find.text("Previous"), driver);
        TestUtils.clickToElement(find.text("Previous"), driver);

        // Check that the value has been updated
        Thread.sleep(3000);
        driver.executeScript("flutter:waitForTappable",
                find.byValueKey("_TvStatus_Giga_report_tvoption2_SelectedOption_"));

        // Navigate back to edit values screen
        TestUtils.clickToElement(find.text("Next"), driver);
        TestUtils.clickToElement(find.text("Next"), driver);
        TestUtils.clickToElement(find.text("Next"), driver);
        TestUtils.clickToElement(find.text("Next"), driver);
    }
}
