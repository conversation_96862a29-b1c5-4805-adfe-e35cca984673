import json
import boto3
import base64
import os


s3 = boto3.client("s3")
gigaFolder = "GigaCheck"
coverageFolder = "Coverage"
technicianFolder = 'TechnicianTip'


def lambda_handler(event, context):
    try:

        #Query params are passed to decide bucket name depending on environment
        query_params = event.get('queryStringParameters',{})

        environment = query_params.get("environment")
        market = query_params.get("market")

        #Validate query parameters
        missing = []
        if not environment:
            missing.append("environment")
        if not market:
            missing.append("market")

        if missing:
            return{
                'statusCode': 400,
                'body': f"Missing required query parameter(s): {', '.join(missing)}"
            }

        #For Technician Tip/BEW call this is a mock response
        useCase = query_params.get("useCase")
        if useCase == 'mockApiUrl':
            return {
                'statusCode': 201,
                'body': json.dumps({"message": "Created"})
            }
        bucket_name = "".join(['techtom-',environment,'-test-automation'])

        body = json.loads(event["body"])

        if market == 'Germany':
            work_order = body["auftragsnr"]
            dms_doc = body["dmsDokument"]
            filename = dms_doc["dateiName"]
            file_content_base64 = dms_doc["dateiInhalt"]
        else:
            work_order = body["workOrderNumber"]
            filename = body["fileName"]
            file_content_base64 = body["file"]

        #Splitting the filename
        name, ext = os.path.splitext(filename)
        filename_split = name.split('-')[0]

        #File name to upload in S3 bucket with work order
        filename_wo = f"{name}-{work_order}{ext}"

        #Condition to check for Gigacheck or Coverage or Technician Tip
        if filename_split == 'GigaCheck':
            subfolder = gigaFolder
        elif filename_split == 'WLANCheck':
            subfolder = coverageFolder
        else:
            subfolder = technicianFolder

        #Decoding the pdf content (Body)
        content_bytes = base64.b64decode(file_content_base64)

        #S3 key (Key)
        s3_key = f"{market}/{subfolder}/{filename_wo}"

        #ContentType
        content_type = "application/pdf"

        s3.put_object(
            Bucket=bucket_name,
            Key=s3_key,
            Body=content_bytes,
            ContentType=content_type
        )

        if market == 'Germany':
            return {
                'statusCode': 200,
                'body': json.dumps({
                    'meldungen': [{'code': 0, 'text': "Mocked api call for send report"}]
                })
            }
        else:
            return {
                'statusCode': 200,
                'body': json.dumps({
                    "isSucceeded": True,
                    "message": "Mocked api for send report successful",
                    "statusCode": 200,
                    "data": "Automation successful"
                })
            }

    except Exception as e:
        print(f"Error: {e}") # Log the actual error
        return {
            'statusCode': 500,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps({
                "message": "Internal server error",
                "error": str(e) # Include error details for debugging (remove in production)
            })
        }
