package appium;

import java.time.Duration;
import org.openqa.selenium.By;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.testng.annotations.*;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.remote.SupportsContextSwitching;

public class LocationPermissions {
    private AndroidDriver driver;
    private WebDriverWait wait;

    @BeforeClass
    @Parameters({ "language", "locale", "testName" })
    public void setup(String language, String locale, String testName) throws Exception {
        driver = DriverManager.getTechTomDriver(language, locale, testName);
        wait = new WebDriverWait(driver,
                Duration.ofSeconds(60));
    }

    @Test
    public void LocationPermissionsTest() throws InterruptedException {
        // Switch context in order to be able to interact with the native overlay
        ((SupportsContextSwitching) driver).context("NATIVE_APP");

        wait.until(ExpectedConditions.elementToBeClickable(By.xpath(
                "//*[@resource-id='com.android.permissioncontroller:id/permission_allow_foreground_only_button']")));
        driver.findElement(By.xpath(
                        "//*[@resource-id='com.android.permissioncontroller:id/permission_allow_foreground_only_button']"))
                .click();
        ((SupportsContextSwitching) driver).context("FLUTTER");
    }
}
