package utils;

import java.time.Duration;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import org.openqa.selenium.By;
import org.openqa.selenium.Dimension;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.interactions.PointerInput;
import org.openqa.selenium.interactions.PointerInput.Origin;
import org.openqa.selenium.interactions.Sequence;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;

import io.appium.java_client.AppiumDriver;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.remote.SupportsContextSwitching;
import pro.truongsinh.appium_flutter.finder.FlutterElement;

public class TestUtils {
    public static void clickToElement(FlutterElement element, AppiumDriver driver) throws InterruptedException {
        WebElement finder = (WebElement) driver.executeScript("flutter:waitFor", element);
        Thread.sleep(2000);
        finder.click();
        Thread.sleep(2000);
    }

    public static void clickToAndroidElement(By locator, WebDriverWait wait) throws InterruptedException {
        Thread.sleep(2000);
        wait.until(ExpectedConditions.elementToBeClickable(
                locator))
                .click();
        Thread.sleep(2000);
    }

    public static void fillFlutterTextField(FlutterElement element, AppiumDriver driver, String text) {
        WebElement finder = (WebElement) driver.executeScript("flutter:waitFor", element, 2000);
        finder.click();
        driver.executeScript("flutter:enterText", text);
    }

    public static void scrollDownUntilElementIsVisible(FlutterElement element, AppiumDriver driver) {
        driver.executeScript("flutter:scrollUntilVisible", element,
                new HashMap<String, Object>() {
                    {
                        put("item", element);
                        put("dxScroll", 90);
                        put("dyScroll", -400);
                    }
                });
    }

    public static void scrollUpUntilFlutterElementIsVisible(FlutterElement element, AppiumDriver driver) {
        driver.executeScript("flutter:scrollUntilVisible", element,
                new HashMap<String, Object>() {
                    {
                        put("item", element); // Element to bring into view
                        put("dxScroll", 0); // No horizontal scroll
                        put("dyScroll", 400); // Positive Y = swipe up (scrolls down)
                    }
                });
    }

    public static void drawSignature(AppiumDriver driver) {
        // 1. Switch to native context FIRST
        ((SupportsContextSwitching) driver).context("NATIVE_APP");

        // 2. Now get screen size
        Dimension screenSize = driver.manage().window().getSize();
        int screenWidth = screenSize.getWidth();
        int screenHeight = screenSize.getHeight();

        // 3. Estimate starting position
        int startX = screenWidth / 4;
        int startY = screenHeight / 2;

        // 4. Perform touch actions
        PointerInput finger = new PointerInput(PointerInput.Kind.TOUCH, "finger");
        Sequence draw = new Sequence(finger, 1);

        draw.addAction(finger.createPointerMove(Duration.ofMillis(0), PointerInput.Origin.viewport(), startX, startY));
        draw.addAction(finger.createPointerDown(PointerInput.MouseButton.LEFT.asArg()));

        // 5. simulate drawing a curve or line
        draw.addAction(finger.createPointerMove(Duration.ofMillis(200), PointerInput.Origin.viewport(), startX + 100,
                startY + 50));
        draw.addAction(
                finger.createPointerMove(Duration.ofMillis(200), PointerInput.Origin.viewport(), startX + 200, startY));
        draw.addAction(finger.createPointerMove(Duration.ofMillis(200), PointerInput.Origin.viewport(), startX + 300,
                startY + 50));

        draw.addAction(finger.createPointerUp(PointerInput.MouseButton.LEFT.asArg()));

        driver.perform(List.of(draw));

        // 6. Switch back to Flutter context
        ((SupportsContextSwitching) driver).context("FLUTTER");
    }

    public static void scrollUpAndroid(AppiumDriver driver) {
        PointerInput finger = new PointerInput(PointerInput.Kind.TOUCH, "finger");
        Sequence swipe = new Sequence(finger, 1);

        int screenHeight = driver.manage().window().getSize().height;
        int screenWidth = driver.manage().window().getSize().width;

        int startX = screenWidth / 2;
        int startY = (int) (screenHeight * 0.8);
        int endY = (int) (screenHeight * 0.2);

        swipe.addAction(finger.createPointerMove(Duration.ZERO, PointerInput.Origin.viewport(), startX, startY));
        swipe.addAction(finger.createPointerDown(PointerInput.MouseButton.LEFT.asArg()));
        swipe.addAction(finger.createPointerMove(Duration.ofMillis(500), PointerInput.Origin.viewport(), startX, endY));
        swipe.addAction(finger.createPointerUp(PointerInput.MouseButton.LEFT.asArg()));

        driver.perform(Arrays.asList(swipe));
    }

    public static void dismissSystemBottomSheet(AndroidDriver driver) throws InterruptedException {
        try {
            // Switch to Native Context
            driver.context("NATIVE_APP");

            // Get screen size
            Dimension screenSize = driver.manage().window().getSize();
            int screenWidth = screenSize.getWidth();
            int screenHeight = screenSize.getHeight();

            // Tap slightly above the middle (outside the bottom sheet)
            int x = screenWidth / 2;
            int y = (int) (screenHeight * 0.4);

            // Create Pointer Input for touch gesture
            PointerInput finger = new PointerInput(PointerInput.Kind.TOUCH, "finger");

            Sequence tap = new Sequence(finger, 1)
                    .addAction(finger.createPointerMove(Duration.ZERO, Origin.viewport(), x, y))
                    .addAction(finger.createPointerDown(PointerInput.MouseButton.LEFT.asArg()))
                    .addAction(finger.createPointerUp(PointerInput.MouseButton.LEFT.asArg()));

            driver.perform(Arrays.asList(tap));

            // Wait briefly to ensure dismissal
            Thread.sleep(1000);

            // Switch back to Flutter Context
            driver.context("FLUTTER");

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
