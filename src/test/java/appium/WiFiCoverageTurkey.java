package appium;

import static pro.truongsinh.appium_flutter.finder._FinderRawMethods.byValueKey;

import org.openqa.selenium.By;
import org.openqa.selenium.Dimension;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.Point;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.testng.Reporter;
import org.testng.annotations.*;
import org.testng.annotations.Parameters;

import java.time.Duration;
import java.util.List;
import java.util.Set;

import io.appium.java_client.AppiumDriver;
import io.appium.java_client.TouchAction;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.touch.TapOptions;
import io.appium.java_client.touch.offset.PointOption;
import pro.truongsinh.appium_flutter.FlutterFinder;
import pro.truongsinh.appium_flutter.finder.FlutterElement;
import utils.TestUtils;

public class WiFiCoverageTurkey {
    private FlutterFinder find;
    private AndroidDriver driver;

    @BeforeClass
    @Parameters({"language", "locale", "testName"})
    public void setup(String language, String locale, String testName) throws Exception {
        driver = DriverManager.getTechTomDriver(language, locale, testName);
        find = new FlutterFinder(driver);
    }

    @Test
    public  void wifiCoverageTrTest() throws InterruptedException{

        //Go to Test & Reports Screen
        TestUtils.clickToElement(find.byValueKey("_Tests&Reports_Tab_"), driver);

        //Start WiFi Coverage Flow
        TestUtils.clickToElement(find.byValueKey("_WiFiCoverageCheck_Button_"), driver);

        //Start Coverage Test
        TestUtils.clickToElement(find.text("Start Coverage Test"), driver);

        //Start scan for the router
        TestUtils.clickToElement(find.text("Start scan"), driver);

        //
        // Wait for app/webview to load
        Thread.sleep(5000);

        //Click on close button
        TestUtils.clickToElement(find.byValueKey("_CloseButton_"), driver);
        TestUtils.clickToElement(find.byValueKey("_Cancel_"), driver);

        // List available contexts
        Set<String> contexts = driver.getContextHandles();
        // goes to Gradle problems-report.html
        System.out.println("Available contexts: " + contexts);

        driver.context("WEBVIEW_com.vodafone.techtom.tr.uat");
        //driver.findElement(By.cssSelector("#main-content > div.button__wrapper > div > button")).click();
        JavascriptExecutor js = (JavascriptExecutor) driver;
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(15));
        WebElement goButton = wait.until(ExpectedConditions.elementToBeClickable(By.cssSelector("button.button.background-primary-hover.text-primary")));

        //goButton.click();
        js.executeScript("arguments[0].click();", goButton);

        // --- Wait until results stabilize ---
        WebDriverWait waitResults = new WebDriverWait(driver, Duration.ofSeconds(120));

        String[] finalSpeeds = waitResults.until(d -> {
            try {
                List<WebElement> elems = d.findElements(By.cssSelector("div.number.monochrome-primary"));
                if (elems.size() >= 2) {
                    String d1 = elems.get(0).getText().trim();
                    String u1 = elems.get(1).getText().trim();

                    // ignore empty values
                    if (!d1.isEmpty() && !u1.isEmpty()) {
                        try { Thread.sleep(3000); } catch (InterruptedException e) {}

                        // Refetch elements to avoid stale element reference
                        List<WebElement> freshElems = d.findElements(By.cssSelector("div.number.monochrome-primary"));
                        if (freshElems.size() >= 2) {
                            String d2 = freshElems.get(0).getText().trim();
                            String u2 = freshElems.get(1).getText().trim();

                            if (d1.equals(d2) && u1.equals(u2)) {
                                return new String[]{d2, u2}; // stable
                            }
                        }
                    }
                }
            } catch (Exception e) {
                // Handle stale element or other exceptions by continuing to wait
                System.out.println("Retrying due to: " + e.getMessage());
            }
            return null; // keep waiting
        });

        System.out.println("✅ Download Speed: " + finalSpeeds[0]);
        System.out.println("✅ Upload Speed: " + finalSpeeds[1]);

        // Wait a bit more to ensure the webview is completely done
        Thread.sleep(2000);

        // Switch back to Flutter with verification
        switchToFlutterContext();

        //Click on close button
        System.out.println("Clicking close button...");
        TestUtils.clickToElement(find.byValueKey("_CloseButton1_"), driver);

        // Wait for UI to stabilize after closing
        Thread.sleep(3000);
        System.out.println("Closed speed test dialog, waiting for UI to stabilize...");

        // Check if we're back to the main coverage screen by looking for expected elements
        try {
            // Wait for the main screen to be ready - try the pattern from working WiFiCoverageCheck
            driver.executeScript("flutter:waitFor", find.text("Initial check"), 15000);
            System.out.println("Back on Initial check screen");
        } catch (Exception e) {
            System.out.println("Initial check text not found, trying WiFi Coverage...");
            try {
                driver.executeScript("flutter:waitFor", find.text("WiFi Coverage"), 10000);
                System.out.println("Back on WiFi Coverage screen");
            } catch (Exception e2) {
                System.out.println("WiFi Coverage text not found, trying Coverage...");
                try {
                    driver.executeScript("flutter:waitFor", find.text("Coverage"), 5000);
                    System.out.println("Found Coverage text");
                } catch (Exception e3) {
                    System.out.println("No expected screen text found, proceeding anyway...");
                }
            }
        }

        // Try to find the router expand button with more patience
        System.out.println("Looking for router expand button...");
        try {
            driver.executeScript("flutter:waitFor", find.byValueKey("_ExpandOrCollapseRouter_Button_"), 15000);
            System.out.println("Found router expand button, clicking it...");
            TestUtils.clickToElement(find.byValueKey("_ExpandOrCollapseRouter_Button_"), driver);

            // Check if speedtest results are already there
            System.out.println("Checking for speed test results...");
            driver.executeScript("flutter:waitFor", find.byValueKey("_DownloadSpeed_Router_"), 5000);
            driver.executeScript("flutter:waitFor", find.byValueKey("_UploadSpeed_Router_"), 5000);
            System.out.println("Speed test results found!");

            // Collapse the item
            System.out.println("Collapsing router item...");
            TestUtils.clickToElement(find.byValueKey("_ExpandOrCollapseRouter_Button_"), driver);

        } catch (Exception e) {
            System.err.println("Could not find or interact with router expand button: " + e.getMessage());
            // Let's see what elements are actually available
            debugAvailableElements();

            // Try alternative router button patterns
            System.out.println("Trying alternative router button patterns...");
            String[] alternativeRouterKeys = {
                "_Router_Button_",
                "_RouterButton_",
                "_ExpandRouter_",
                "_CollapseRouter_",
                "_Router_ExpandCollapse_"
            };

            boolean foundAlternative = false;
            for (String altKey : alternativeRouterKeys) {
                try {
                    driver.executeScript("flutter:waitFor", find.byValueKey(altKey), 3000);
                    System.out.println("Found alternative router button: " + altKey);
                    TestUtils.clickToElement(find.byValueKey(altKey), driver);
                    foundAlternative = true;
                    break;
                } catch (Exception altE) {
                    // Continue trying other alternatives
                }
            }

            if (!foundAlternative) {
                System.out.println("No router button found. Test may need manual verification.");
                // Don't fail the test completely, just log the issue
            }
        }




    }

    public static String[] waitForSpeedTestResults(AppiumDriver driver) {
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(120));

        return wait.until(d -> {
            try {
                List<WebElement> elems = d.findElements(By.cssSelector("div.number.monochrome-primary"));

                if (elems.size() >= 2) {
                    String d1 = elems.get(0).getText().trim();
                    String u1 = elems.get(1).getText().trim();

                    if (!d1.isEmpty() && !u1.isEmpty()) {
                        try { Thread.sleep(4000); } catch (InterruptedException ignored) {}

                        // Refetch elements to avoid stale element reference
                        List<WebElement> freshElems = d.findElements(By.cssSelector("div.number.monochrome-primary"));
                        if (freshElems.size() >= 2) {
                            String d2 = freshElems.get(0).getText().trim();
                            String u2 = freshElems.get(1).getText().trim();

                            // values must be stable
                            if (d1.equals(d2) && u1.equals(u2)) {
                                // wait extra time for UI rendering (final numbers)
                                try { Thread.sleep(5000); } catch (InterruptedException ignored) {}
                                return new String[]{d2, u2};
                            }
                        }
                    }
                }
            } catch (Exception e) {
                // Handle stale element or other exceptions by continuing to wait
                System.out.println("Retrying speed test wait due to: " + e.getMessage());
            }
            return null; // keep waiting
        });
    }

    private void switchToFlutterContext() throws InterruptedException {
        try {
            // List available contexts to debug
            Set<String> contexts = driver.getContextHandles();
            System.out.println("Available contexts before switching: " + contexts);

            // Switch to Flutter context
            driver.context("FLUTTER");

            // Verify the switch was successful
            String currentContext = driver.getContext();
            System.out.println("Current context after switch: " + currentContext);

            // Wait a moment for context to stabilize
            Thread.sleep(1000);

        } catch (Exception e) {
            System.err.println("Error switching to Flutter context: " + e.getMessage());
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * Alternative method using a more robust approach for waiting for speed test completion
     * This method can be used instead of the inline waiting logic if needed
     */
    private String[] waitForSpeedTestCompletionRobust() {
        WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(120));

        return wait.until(d -> {
            try {
                // Check if speed test is still running by looking for loading indicators
                List<WebElement> loadingElements = d.findElements(By.cssSelector(".loading, .spinner, .progress"));
                if (!loadingElements.isEmpty()) {
                    return null; // Still loading, keep waiting
                }

                // Look for the speed results
                List<WebElement> speedElements = d.findElements(By.cssSelector("div.number.monochrome-primary"));
                if (speedElements.size() >= 2) {
                    String download = speedElements.get(0).getText().trim();
                    String upload = speedElements.get(1).getText().trim();

                    // Check if we have valid numeric results
                    if (!download.isEmpty() && !upload.isEmpty() &&
                        !download.equals("0") && !upload.equals("0") &&
                        !download.equals("--") && !upload.equals("--")) {

                        // Wait a bit and check again for stability
                        try { Thread.sleep(3000); } catch (InterruptedException ignored) {}

                        // Refetch to ensure values are still the same
                        List<WebElement> freshElements = d.findElements(By.cssSelector("div.number.monochrome-primary"));
                        if (freshElements.size() >= 2) {
                            String downloadCheck = freshElements.get(0).getText().trim();
                            String uploadCheck = freshElements.get(1).getText().trim();

                            if (download.equals(downloadCheck) && upload.equals(uploadCheck)) {
                                return new String[]{download, upload};
                            }
                        }
                    }
                }
            } catch (Exception e) {
                System.out.println("Retrying speed test completion check: " + e.getMessage());
            }
            return null; // Keep waiting
        });
    }

    /**
     * Debug method to help identify what elements are available when the expected ones are not found
     */
    private void debugAvailableElements() {
        try {
            System.out.println("=== DEBUG: Available Flutter Elements ===");

            // Try to find common text elements that might indicate current screen
            String[] commonTexts = {"WiFi Coverage", "Coverage", "Router", "Start", "Close", "Back", "Continue", "Test"};
            for (String text : commonTexts) {
                try {
                    driver.executeScript("flutter:waitFor", find.text(text), 1000);
                    System.out.println("Found text: " + text);
                } catch (Exception e) {
                    // Element not found, continue
                }
            }

            // Try to find common button elements
            String[] commonKeys = {"_CloseButton_", "_BackButton_", "_ContinueButton_", "_StartButton_", "_Router_"};
            for (String key : commonKeys) {
                try {
                    driver.executeScript("flutter:waitFor", find.byValueKey(key), 1000);
                    System.out.println("Found element with key: " + key);
                } catch (Exception e) {
                    // Element not found, continue
                }
            }

            System.out.println("=== END DEBUG ===");

        } catch (Exception e) {
            System.err.println("Error during debugging: " + e.getMessage());
        }
    }

}