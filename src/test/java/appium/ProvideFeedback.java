package appium;

import org.testng.annotations.*;
import io.appium.java_client.android.AndroidDriver;
import io.appium.java_client.remote.SupportsContextSwitching;
import pro.truongsinh.appium_flutter.FlutterFinder;
import utils.TestUtils;

public class ProvideFeedback {
    private FlutterFinder find;
    private AndroidDriver driver;

    @BeforeClass
    @Parameters({ "language", "locale", "testName" })
    public void setup(String language, String locale, String testName) throws Exception {
        driver = DriverManager.getTechTomDriver(language, locale, testName);
        find = new FlutterFinder(driver);
    }

    @Test
    public void provideFeedbackTest() throws InterruptedException {
        // Go to Information Screen
        TestUtils.clickToElement(find.byType("IconButton"), driver);

        // Tap on feedback button
        TestUtils.clickToElement(find.text("Feedback"), driver);

        // Press on Give Feedback button
        TestUtils.clickToElement(find.text("Give feedback"), driver);

        // Select Give feedback option
        TestUtils.clickToElement(find.text("Give feedback"), driver);

        // Type feedback
        TestUtils.fillFlutterTextField(find.byValueKey("_Feedback_TextField_"), driver, "This is my feedback");

        // Tap on Send feedback button and check that success message is displayed
        TestUtils.clickToElement(find.text("Send feedback"), driver);
        Thread.sleep(3000);

        // Go back from email app
        ((SupportsContextSwitching) driver).context("NATIVE_APP");
        driver.navigate().back();
        driver.navigate().back();
        ((SupportsContextSwitching) driver).context("FLUTTER");
        Thread.sleep(3000);

        // Check that success message got displayed
        driver.executeScript("flutter:waitFor", find.text("Thank you"), 2000);
        Thread.sleep(3000);
    }
}
