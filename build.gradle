plugins {
    id 'java'
    id 'application'
}

group = 'com.vodafone.techtom.appium'
version = '1.0-SNAPSHOT'
sourceCompatibility = '17'
targetCompatibility = '17'

repositories {
    mavenCentral()
    maven {
        url "https://jitpack.io"
    }
}

dependencies {
    implementation 'io.appium:java-client:9.0.0'
    implementation 'org.seleniumhq.selenium:selenium-remote-driver:4.14.1'
    testImplementation 'com.googlecode.json-simple:json-simple:1.1'
    testImplementation 'commons-lang:commons-lang:2.6'
    testImplementation 'org.testng:testng:7.11.0'
    testImplementation 'com.saucelabs:sauce_junit:2.1.23'
    implementation 'com.google.code.gson:gson:2.8.9'
    implementation 'com.github.appium:appium-flutter-driver:3.0.0'
    implementation 'com.mashape.unirest:unirest-java:1.4.9'
}

def testngXmlDir = file('src/test/resources')

testngXmlDir.eachFileMatch(~/.*\.xml/) { File xmlFile ->
    def taskName = xmlFile.name

    tasks.register(taskName, Test) {
        useTestNG {
            suites 'src/test/resources/' + taskName
        }
        jvmArgs '-ea'
        testLogging {
            events "passed", "failed", "skipped", "standardOut"
        }
        systemProperties = [
            'tscloud.accessKey': System.getenv('TS_CLOUD_ACCESS_KEY') ?: project.findProperty('tscloud.accessKey'),
            'tscloud.deviceId': System.getenv('TS_CLOUD_DEVICE_ID') ?: project.findProperty('tscloud.deviceId'),
            'tscloud.appUniqueName': System.getenv('TS_CLOUD_APP_UNIQUE_NAME') ?: project.findProperty('tscloud.appUniqueName'),
            'launcherApp': System.getenv('LAUNCHER_APP') ?: project.findProperty('launcherApp')
        ]
    }
}
