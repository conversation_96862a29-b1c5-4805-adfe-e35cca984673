package appium;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;

public class ObservatoryUrlManager {
    private static HttpResponse<InputStream> responseInputStream;
    private static String urlBase = "https://tscloud.vodafone.com/reporter";
    private static String observatoryUrl;

    public static void getAttachmentsByTestID(Object testID) {
        String url = urlBase + "/api/" + testID + "/attachments";
        String zipPath = "attachments_" + testID + ".zip";
        String extractedFolder = "extracted_" + testID;
        
        try {
            responseInputStream = Unirest.get(url)
                    .header("Authorization", "Bearer " + System.getProperty("tscloud.accessKey"))
                    .header("content-type", "application/json")
                    .asBinary();

            writeToFile(zipPath);

            unzip(zipPath, extractedFolder);

            File deviceLog = findDeviceLog(new File(extractedFolder));
            if (deviceLog != null) {
                observatoryUrl = parseObservatoryUrl(deviceLog.getAbsolutePath());
            } else {
                System.out.println("device.log not found in the extracted files.");
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            deleteFileOrDirectory(new File(zipPath));
            deleteFileOrDirectory(new File(extractedFolder));
        }
    }

    private static void writeToFile(String destination) {
        try (InputStream inputStream = responseInputStream.getRawBody();
                FileOutputStream fileOutputStream = new FileOutputStream(destination)) {

            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                fileOutputStream.write(buffer, 0, bytesRead);
            }

            System.out.println("File downloaded: " + destination);

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static void unzip(String zipFilePath, String destDir) {
        File dir = new File(destDir);
        if (!dir.exists())
            dir.mkdirs();

        byte[] buffer = new byte[4096];

        try (ZipInputStream zis = new ZipInputStream(new FileInputStream(zipFilePath))) {
            ZipEntry zipEntry = zis.getNextEntry();

            while (zipEntry != null) {
                File newFile = new File(destDir, zipEntry.getName());

                new File(newFile.getParent()).mkdirs();

                try (FileOutputStream fos = new FileOutputStream(newFile)) {
                    int len;
                    while ((len = zis.read(buffer)) > 0) {
                        fos.write(buffer, 0, len);
                    }
                }

                zipEntry = zis.getNextEntry();
            }

            zis.closeEntry();
            System.out.println("Extraction completed to: " + destDir);

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static File findDeviceLog(File directory) {
        File[] files = directory.listFiles();

        if (files == null)
            return null;

        for (File file : files) {
            if (file.isDirectory()) {
                File found = findDeviceLog(file);
                if (found != null)
                    return found;
            } else if (file.getName().equalsIgnoreCase("device.log")) {
                return file;
            }
        }
        return null;
    }

    private static String parseObservatoryUrl(String logFilePath) {
        String regex = "http://127\\.0\\.0\\.1:\\d+/\\S+/";
        Pattern pattern = Pattern.compile(regex);
        List<String> urls = new ArrayList<>();

        try {
            String content = Files.readString(Paths.get(logFilePath));
            Matcher matcher = pattern.matcher(content);

            while (matcher.find()) {
                urls.add(matcher.group());
            }

            if (urls.isEmpty()) {
                return null;
            }

            // Always use the last matched URL
            String url = urls.get(urls.size() - 1);
            String formattedUrl = url.replace("http", "ws") + "ws";
            return formattedUrl;

        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String getObservatoryUrl() {
        return observatoryUrl;
    }

    private static void deleteFileOrDirectory(File fileOrDirectory) {
        if (fileOrDirectory.isDirectory()) {
            File[] files = fileOrDirectory.listFiles();
            if (files != null) {
                for (File file : files) {
                    deleteFileOrDirectory(file);
                }
            }
        }
        if (fileOrDirectory.delete()) {
            System.out.println("Deleted: " + fileOrDirectory.getAbsolutePath());
        } else {
            System.out.println("Failed to delete: " + fileOrDirectory.getAbsolutePath());
        }
    }
}
