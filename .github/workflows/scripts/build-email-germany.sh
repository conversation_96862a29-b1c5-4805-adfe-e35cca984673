#!/usr/bin/env bash
set -euo pipefail

TEMPLATE_FILE="$1"

status_emoji () {
  case "$1" in
    success) echo "✅" ;;
    failure) echo "❌" ;;
    *)       echo "⚠️" ;;
  esac
}
status_chip () {
  case "$1" in
    success) echo '<span class="chip chip--ok">success</span>' ;;
    failure) echo '<span class="chip chip--fail">failure</span>' ;;
    *)       echo '<span class="chip chip--warn">unknown</span>' ;;
  esac
}

# Derive emojis/chips
change_lang_emoji=$(status_emoji "$CHANGE_LANG");    change_lang_chip=$(status_chip "$CHANGE_LANG")
giga_check_emoji=$(status_emoji "$GIGA_CHECK");     giga_check_chip=$(status_chip "$GIGA_CHECK")
feedback_emoji=$(status_emoji "$FEEDBACK");       feedback_chip=$(status_chip "$FEEDBACK")
wifi_coverage_emoji=$(status_emoji "$WIFI_COVERAGE");  wifi_coverage_chip=$(status_chip "$WIFI_COVERAGE")
technician_tip_emoji=$(status_emoji "$TECHNICIAN_TIP"); technician_tip_chip=$(status_chip "$TECHNICIAN_TIP")

# Pass/fail counts
PASS=$(printf "%s\n%s\n%s\n%s\n%s\n" \
  "$CHANGE_LANG" "$GIGA_CHECK" "$FEEDBACK" "$WIFI_COVERAGE" "$TECHNICIAN_TIP" | grep -c '^success$' || true)
FAIL=$(printf "%s\n%s\n%s\n%s\n%s\n" \
  "$CHANGE_LANG" "$GIGA_CHECK" "$FEEDBACK" "$WIFI_COVERAGE" "$TECHNICIAN_TIP" | grep -c '^failure$' || true)

SUBJECT="TechTOM Germany Automation Results: ${PASS} passed, ${FAIL} failed"
echo "SUBJECT=$SUBJECT"

RUN_URL="${GITHUB_SERVER_URL}/${GITHUB_REPOSITORY}/actions/runs/${GITHUB_RUN_ID}"

export change_lang_emoji giga_check_emoji feedback_emoji wifi_coverage_emoji technician_tip_emoji
export change_lang_chip giga_check_chip feedback_chip wifi_coverage_chip technician_tip_chip
export GITHUB_WORKFLOW GITHUB_REPOSITORY GITHUB_REF_NAME GITHUB_SHA RUN_URL
export PASS FAIL

envsubst '${change_lang_emoji} ${giga_check_emoji} ${feedback_emoji} ${wifi_coverage_emoji} ${technician_tip_emoji} ${change_lang_chip} ${giga_check_chip} ${feedback_chip} ${wifi_coverage_chip} ${technician_tip_chip} ${GITHUB_WORKFLOW} ${RUN_URL} ${GITHUB_SHA} ${GITHUB_REF_NAME} ${GITHUB_REPOSITORY} ${PASS} ${FAIL}' \
  < "$TEMPLATE_FILE"
